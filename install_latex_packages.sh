#!/bin/bash

# Install comprehensive LaTeX packages for DeepDocX
echo "Installing LaTeX packages for DeepDocX..."

# Update package list
sudo apt-get update

# Install comprehensive LaTeX distribution
sudo apt-get install -y texlive-full

# If texlive-full is too large, install essential packages individually:
# sudo apt-get install -y texlive-latex-base
# sudo apt-get install -y texlive-latex-recommended
# sudo apt-get install -y texlive-latex-extra
# sudo apt-get install -y texlive-fonts-recommended
# sudo apt-get install -y texlive-fonts-extra
# sudo apt-get install -y texlive-science
# sudo apt-get install -y texlive-pictures
# sudo apt-get install -y texlive-bibtex-extra

# Install specific packages that are commonly needed
sudo apt-get install -y texlive-science  # For algorithm packages
sudo apt-get install -y texlive-pictures  # For graphics packages
sudo apt-get install -y texlive-fonts-extra  # Additional fonts

echo "LaTeX packages installation completed!"
echo "You can now compile documents with advanced LaTeX features."
