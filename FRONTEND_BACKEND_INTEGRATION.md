# Frontend-Backend Integration Guide

## Overview

This document outlines the changes made to integrate the frontend with the Flask backend API, removing all mock data and implementing real API calls.

## Changes Made

### 1. API Client Configuration
- **File**: `lib/api/client.ts`
- **Changes**: 
  - Updated base URL to use environment variable `NEXT_PUBLIC_API_URL`
  - Fixed import paths for types
  - Added proper error handling

### 2. Real Data Hooks

#### Documents Hook (`lib/hooks/useDocuments.ts`)
- Replaced mock implementations with real API calls
- Added proper error handling and logging
- Handles different response formats from backend
- Includes document versions management

#### New Hooks Created:
- **`lib/hooks/useFolders.ts`**: Folder management (create, update, delete, list)
- **`lib/hooks/useFiles.ts`**: File upload, download, delete operations
- **`lib/hooks/useConversations.ts`**: AI chat/conversation management

### 3. Authentication System
- **File**: `lib/contexts/auth-context.tsx`
- **Features**:
  - JWT token management
  - User registration and login
  - Session persistence
  - Automatic route protection
  - Sign out functionality
  - Loading states during auth checks
  - Automatic redirects (signin → dashboard, dashboard → signin if not authenticated)

### 4. Updated Services

#### LaTeX Service (`lib/services/latex.ts`)
- Replaced mock compilation with real API calls
- Uses backend `/api/latex/compile` endpoint
- Real PDF download functionality

#### Sharing Service (`lib/services/sharing.ts`)
- Already implemented with real API calls
- Document sharing with permissions
- Share link generation

### 5. Component Updates

#### Research Dashboard (`components/research-dashboard.tsx`)
- **Removed**: All mock data (mockDocuments, initialFolders, chatMessages)
- **Added**: Real hooks integration
- **Updated**: Chat messages to use real conversation data
- **Fixed**: File and folder display to match database schema

#### Authentication Pages
- **`components/auth/signin-page.tsx`**: Real authentication with error handling
- **`components/auth/signup-page.tsx`**: Real user registration
- **`components/auth/protected-route.tsx`**: Route protection wrapper
- **`components/auth/auth-loading.tsx`**: Loading component during auth checks

### 6. Layout Updates
- **File**: `app/layout.tsx`
- **Added**: AuthProvider wrapper for authentication context
- **Added**: Toaster for notifications

## Environment Configuration

### Required Environment Variables
Create a `.env.local` file with:

```env
NEXT_PUBLIC_API_URL=http://localhost:5000/api
```

## Backend API Endpoints Expected

The frontend now expects these backend endpoints to be available:

### Authentication
- `POST /api/auth/signup` - User registration
- `POST /api/auth/signin` - User login
- `POST /api/auth/signout` - User logout
- `GET /api/auth/me` - Get current user

### Documents
- `GET /api/documents` - List user documents
- `POST /api/documents` - Create new document
- `GET /api/documents/{id}` - Get document details
- `PATCH /api/documents/{id}` - Update document
- `DELETE /api/documents/{id}` - Delete document
- `GET /api/documents/{id}/versions` - Get document versions
- `POST /api/documents/{id}/versions` - Create new version
- `GET /api/documents/{id}/current-version` - Get current version
- `POST /api/documents/{id}/versions/{version_id}/set-current` - Set current version

### Folders
- `GET /api/folders` - List folders
- `POST /api/folders` - Create folder
- `PATCH /api/folders/{id}` - Update folder
- `DELETE /api/folders/{id}` - Delete folder

### Files
- `GET /api/files` - List files
- `POST /api/files/upload` - Upload file
- `DELETE /api/files/{id}` - Delete file
- `GET /api/files/{id}/download` - Download file

### LaTeX Compilation
- `POST /api/latex/compile` - Compile LaTeX to PDF
- `GET /api/latex/download/{document_id}` - Download compiled PDF

### Sharing
- `POST /api/share` - Create document share
- `GET /api/share/{token}` - Access shared document
- `GET /api/share/document/{document_id}` - List document shares
- `DELETE /api/share/{share_id}` - Revoke share

### Conversations
- `GET /api/conversations/document/{document_id}` - Get document conversation
- `GET /api/conversations/{id}/messages` - Get conversation messages
- `POST /api/conversations/{id}/messages` - Send message

## Authentication Flow

### User Registration/Login
1. User fills out signin/signup form
2. Form validation (client-side)
3. API call to `/api/auth/signin` or `/api/auth/signup`
4. JWT token received and stored in localStorage
5. Token added to API client headers
6. User redirected to `/dashboard`
7. Auth context updates with user data

### Route Protection
1. **Public Routes**: `/`, `/auth/signin`, `/auth/signup`, `/forgot-password`
2. **Protected Routes**: `/dashboard` and all other routes
3. **Auto-redirect Logic**:
   - Not authenticated + protected route → redirect to `/auth/signin`
   - Authenticated + auth pages → redirect to `/dashboard`
   - Dashboard requires authentication via `ProtectedRoute` wrapper

### Session Management
1. **App Load**: Check localStorage for token
2. **Token Found**: Validate with `/api/auth/me`
3. **Token Valid**: Set user data and continue
4. **Token Invalid**: Clear token and redirect to signin
5. **No Token**: Redirect to signin if accessing protected route

### Logout Flow
1. User clicks "Sign Out"
2. API call to `/api/auth/signout`
3. Clear localStorage token
4. Clear API client token
5. Reset user state
6. Redirect to `/auth/signin`

## Data Flow

1. **Authentication**: User signs in → JWT token stored → Used for all API calls
2. **Documents**: Load from `/api/documents` → Display in UI → CRUD operations via API
3. **Real-time Updates**: UI updates immediately after successful API calls
4. **Error Handling**: All API errors are caught and displayed to user via toast notifications

## Testing the Integration

1. Start your Flask backend on `http://localhost:5000`
2. Ensure all required endpoints are implemented
3. Start the frontend: `npm run dev`
4. Test authentication flow
5. Test document creation, editing, and management
6. Test file upload and folder management
7. Test LaTeX compilation
8. Test sharing functionality

## Key Benefits

- **No Mock Data**: All data comes from real backend
- **Real Authentication**: Proper JWT-based auth system
- **Error Handling**: Comprehensive error handling and user feedback
- **Type Safety**: TypeScript types match backend schema
- **Scalable**: Easy to add new features and endpoints
- **Production Ready**: Ready for deployment with real backend

## Next Steps

1. Implement the Flask backend according to `backend-documentation.md`
2. Set up Supabase database with the provided schema
3. Test all endpoints with the frontend
4. Add any missing error handling or edge cases
5. Deploy both frontend and backend
