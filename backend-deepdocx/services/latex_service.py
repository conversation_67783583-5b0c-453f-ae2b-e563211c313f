"""
Enhanced LaTeX compilation service with advanced features for professional document creation
"""

import os
import tempfile
import subprocess
import uuid
import shutil
import re
from flask import current_app
from services.supabase_client import get_service_supabase
import logging

logger = logging.getLogger(__name__)

class LatexService:

    # Comprehensive LaTeX packages available in texlive-latex-extra
    AVAILABLE_PACKAGES = [
        # Core math and symbols
        'amsmath', 'amsfonts', 'amssymb', 'amsthm', 'mathtools',

        # Graphics and figures
        'graphicx', 'float', 'caption', 'subcaption', 'wrapfig',
        'tikz', 'pgfplots', 'pgf',

        # Page layout and formatting
        'geometry', 'fancyhdr', 'setspace', 'parskip',

        # Colors and boxes
        'xcolor', 'tcolorbox', 'framed', 'mdframed',

        # Tables
        'booktabs', 'array', 'longtable', 'tabularx', 'multirow',

        # Lists and enumerations
        'enumitem', 'paralist',

        # Code and algorithms
        'listings', 'verbatim', 'fancyvrb',

        # Bibliography and references
        'natbib', 'biblatex', 'cite',

        # Links and URLs
        'hyperref', 'url', 'xurl',

        # Fonts and encoding
        'fontenc', 'inputenc', 'textcomp', 'lmodern',

        # Language support
        'babel', 'polyglossia',

        # Typography improvements
        'microtype', 'csquotes',

        # Scientific packages
        'siunitx', 'chemformula', 'mhchem',

        # Additional useful packages
        'appendix', 'afterpage', 'placeins', 'rotating',
        'pdflscape', 'lscape', 'multicol', 'changepage'
    ]
    
    @staticmethod
    def compile_latex(document_id: str, latex_code: str, user_id: str = None):
        """Enhanced LaTeX compilation with multi-pass support and better error handling"""
        print(f"=== STARTING LATEX COMPILATION ===")
        print(f"Document ID: {document_id}")
        print(f"User ID: {user_id}")
        print(f"LaTeX code length: {len(latex_code)} characters")
        logger.info(f"=== STARTING LATEX COMPILATION ===")
        logger.info(f"Document ID: {document_id}")
        logger.info(f"User ID: {user_id}")
        logger.info(f"LaTeX code length: {len(latex_code)} characters")
        try:
            # Create temporary directory for compilation
            with tempfile.TemporaryDirectory() as temp_dir:
                # Create LaTeX file
                tex_filename = os.path.join(temp_dir, f"document_{document_id}.tex")
                pdf_filename = os.path.join(temp_dir, f"document_{document_id}.pdf")

                # Copy referenced images to compilation directory
                print(f"Checking user_id: {user_id}")
                if user_id:
                    print(f"Starting image copying for user {user_id}")
                    logger.info(f"Starting image copying for user {user_id}")
                    LatexService._copy_referenced_images(latex_code, temp_dir, user_id)
                else:
                    print("No user_id provided for image copying")
                    logger.warning("No user_id provided for image copying")

                # Remove problematic packages that cause pgfkeys errors
                print("=== REMOVING PROBLEMATIC PACKAGES ===")
                print("Original LaTeX first 500 chars:")
                print(latex_code[:500])
                print("=== END ORIGINAL PREVIEW ===")

                # Copy referenced images to compilation directory
                if user_id:
                    print(f"Starting image copying for user {user_id}")
                    LatexService._copy_referenced_images(latex_code, temp_dir, user_id)

                # Remove problematic packages and environments
                print("=== BEFORE PACKAGE REMOVAL ===")
                print(f"LaTeX contains natbib: {'natbib' in latex_code}")

                # Simple fix: directly remove natbib package
                enhanced_latex = latex_code
                if '\\usepackage{natbib}' in enhanced_latex:
                    print("Found natbib package - removing it")
                    enhanced_latex = enhanced_latex.replace('\\usepackage{natbib}', '% Removed natbib package (no bibliography present)')

                try:
                    enhanced_latex = LatexService._remove_problematic_packages(enhanced_latex)
                    print("=== AFTER PACKAGE REMOVAL ===")
                    print(f"LaTeX contains natbib: {'natbib' in enhanced_latex}")
                    print(f"Enhanced code first 500 chars: {enhanced_latex[:500]}")
                except Exception as e:
                    print(f"=== ERROR IN PACKAGE REMOVAL: {e} ===")
                    # enhanced_latex already has the natbib removed above

                # Write LaTeX code to file with proper encoding handling
                print(f"=== WRITING LATEX TO FILE ===")
                print(f"LaTeX length: {len(enhanced_latex)} characters")

                # Check if LaTeX starts correctly and look for issues around line 51
                lines = enhanced_latex.split('\n')
                print(f"First 10 lines:")
                for i, line in enumerate(lines[0:10], 1):
                    print(f"Line {i}: {line}")

                print(f"Lines around 40-50 (error at 44):")
                for i, line in enumerate(lines[39:50], 40):
                    print(f"Line {i}: {line}")

                print(f"Lines around 85-90:")
                for i, line in enumerate(lines[84:90], 85):
                    print(f"Line {i}: {line}")

                if len(lines) > 180:
                    print(f"Lines around 180-190 (error at 183):")
                    for i, line in enumerate(lines[179:190], 180):
                        print(f"Line {i}: {line}")
                print("=== END PREVIEW ===")

                try:
                    with open(tex_filename, 'w', encoding='utf-8', errors='replace') as tex_file:
                        tex_file.write(enhanced_latex)
                except UnicodeEncodeError:
                    # Fallback: clean the LaTeX code of problematic characters
                    cleaned_latex = LatexService._clean_latex_encoding(enhanced_latex)
                    with open(tex_filename, 'w', encoding='utf-8', errors='replace') as tex_file:
                        tex_file.write(cleaned_latex)

                # Debug: List files in temp directory
                print(f"=== FILES IN COMPILATION DIRECTORY {temp_dir} ===")
                logger.info(f"Files in compilation directory {temp_dir}:")
                for root, dirs, files in os.walk(temp_dir):
                    for file in files:
                        file_path = os.path.join(root, file)
                        rel_path = os.path.relpath(file_path, temp_dir)
                        print(f"  {rel_path}")
                        logger.info(f"  {rel_path}")
                    for dir_name in dirs:
                        print(f"  {dir_name}/ (directory)")
                        logger.info(f"  {dir_name}/ (directory)")

                # Check if pdflatex is available
                try:
                    subprocess.run(['pdflatex', '--version'], capture_output=True, check=True)
                except (subprocess.CalledProcessError, FileNotFoundError):
                    return {
                        'success': False,
                        'pdf_url': None,
                        'errors': ['pdflatex is not installed or not available in PATH. Please install TeX Live or MiKTeX.']
                    }

                # Multi-pass compilation for complex documents
                print(f"Starting LaTeX compilation in directory: {temp_dir}")
                print(f"Current working directory: {os.getcwd()}")
                compilation_success, errors = LatexService._multi_pass_compilation(temp_dir, tex_filename, document_id)

                if compilation_success and os.path.exists(pdf_filename):
                    # Upload PDF to storage and get URL
                    pdf_url = LatexService._upload_pdf_to_storage(document_id, pdf_filename)
                    logger.info(f"Compilation successful for document {document_id}, PDF URL: {pdf_url}")

                    return {
                        'success': True,
                        'pdf_url': pdf_url,
                        'errors': []
                    }
                else:
                    return {
                        'success': False,
                        'pdf_url': None,
                        'errors': errors
                    }

        except subprocess.TimeoutExpired:
            return {
                'success': False,
                'pdf_url': None,
                'errors': ['Compilation timeout - document too complex or contains infinite loops. Try simplifying your document.']
            }
        except Exception as e:
            logger.error(f"Error compiling LaTeX: {e}")
            return {
                'success': False,
                'pdf_url': None,
                'errors': [f'Compilation error: {str(e)}']
            }

    @staticmethod
    def _enhance_latex_code(latex_code: str) -> str:
        """Enhance LaTeX code with safe, commonly available packages"""
        lines = latex_code.split('\n')
        enhanced_lines = []

        # Track if we've found document class and packages
        has_documentclass = False
        has_packages = set()
        preamble_ended = False

        for line in lines:
            stripped = line.strip()

            # Check for document class
            if stripped.startswith('\\documentclass'):
                has_documentclass = True
                enhanced_lines.append(line)
                continue

            # Check for existing packages
            if stripped.startswith('\\usepackage'):
                package_match = stripped.split('{')[1].split('}')[0] if '{' in stripped and '}' in stripped else ''
                if package_match:
                    has_packages.add(package_match)
                enhanced_lines.append(line)
                continue

            # Check if we've reached the document body
            if stripped.startswith('\\begin{document}'):
                preamble_ended = True

                # Add essential packages if not present
                essential_packages = [
                    'amsmath', 'amsfonts', 'amssymb',  # Math
                    'graphicx', 'geometry', 'xcolor',   # Graphics and layout
                    'hyperref'                          # Links
                ]
                if has_documentclass:
                    for package in essential_packages:
                        if package not in has_packages:
                            enhanced_lines.append(f'\\usepackage{{{package}}}')

                enhanced_lines.append(line)
                continue

            enhanced_lines.append(line)

        # If no document class found, add a basic one with essential packages
        if not has_documentclass:
            basic_preamble = [
                '\\documentclass[11pt,a4paper]{article}',
                '% Essential packages for professional document creation',
                '\\usepackage{amsmath,amsfonts,amssymb}',
                '\\usepackage{graphicx}',
                '\\usepackage{geometry}',
                '\\usepackage{xcolor}',
                '\\usepackage{hyperref}',
                '',
                '\\geometry{margin=1in}',
                '\\setlength{\\parindent}{0pt}',
                '\\setlength{\\parskip}{6pt}',
                '',
                '\\begin{document}'
            ]

            # Add the enhanced preamble at the beginning
            enhanced_lines = basic_preamble + enhanced_lines + ['\\end{document}']

        return '\n'.join(enhanced_lines)

    @staticmethod
    def _check_package_availability() -> set:
        """Check which LaTeX packages are available on the system"""
        available_packages = set(LatexService.CORE_PACKAGES)  # Core packages are assumed available

        # Test optional packages by trying to compile a minimal document
        for package in LatexService.OPTIONAL_PACKAGES:
            try:
                with tempfile.TemporaryDirectory() as temp_dir:
                    test_tex = os.path.join(temp_dir, 'test.tex')
                    test_content = f"""
\\documentclass{{article}}
\\usepackage{{{package}}}
\\begin{{document}}
Test
\\end{{document}}
"""
                    with open(test_tex, 'w', encoding='utf-8') as f:
                        f.write(test_content)

                    # Try to compile
                    result = subprocess.run([
                        'pdflatex',
                        '-output-directory', temp_dir,
                        '-interaction=nonstopmode',
                        '-halt-on-error',
                        test_tex
                    ], capture_output=True, text=True, timeout=10)

                    if result.returncode == 0:
                        available_packages.add(package)
                        logger.info(f"Package {package} is available")
                    else:
                        logger.warning(f"Package {package} is not available")

            except Exception as e:
                logger.warning(f"Could not test package {package}: {e}")

        return available_packages

    @staticmethod
    def _multi_pass_compilation(temp_dir: str, tex_filename: str, document_id: str) -> tuple[bool, list]:
        """Perform multi-pass LaTeX compilation for complex documents"""
        errors = []

        try:
            # First pass - main compilation
            logger.info(f"Starting first pass compilation for document {document_id}")
            # Set environment variables for LaTeX compilation
            latex_env = os.environ.copy()
            latex_env['openout_any'] = 'a'  # Allow all file operations
            latex_env['openin_any'] = 'a'   # Allow all file reads

            # Use just the filename since we're running from temp_dir
            tex_basename = os.path.basename(tex_filename)

            result1 = subprocess.run([
                'pdflatex',
                '-output-directory', '.',
                '-interaction=nonstopmode',
                '-file-line-error',
                tex_basename
            ], capture_output=True, text=True, timeout=60, env=latex_env, cwd=temp_dir,
            encoding='utf-8', errors='replace')

            # Check if we need bibliography compilation
            aux_filename = tex_filename.replace('.tex', '.aux')
            needs_bibtex = False

            if os.path.exists(aux_filename):
                try:
                    with open(aux_filename, 'r', encoding='utf-8', errors='replace') as aux_file:
                        aux_content = aux_file.read()
                        if '\\bibdata' in aux_content or '\\citation' in aux_content:
                            needs_bibtex = True
                except (UnicodeDecodeError, IOError):
                    # Skip bibtex if aux file has encoding issues
                    logger.warning(f"Could not read aux file due to encoding issues: {aux_filename}")
                    needs_bibtex = False

            # Run bibtex if needed
            if needs_bibtex:
                logger.info(f"Running bibtex for document {document_id}")
                # Set environment variables to allow file operations
                bibtex_env = os.environ.copy()
                bibtex_env['openout_any'] = 'a'  # Allow all file operations
                bibtex_env['openin_any'] = 'a'   # Allow all file reads

                bibtex_result = subprocess.run([
                    'bibtex',
                    os.path.basename(tex_filename).replace('.tex', '')
                ], cwd=temp_dir, capture_output=True, text=True, timeout=30, env=bibtex_env,
                encoding='utf-8', errors='replace')

                if bibtex_result.returncode != 0:
                    logger.warning(f"Bibtex failed for document {document_id}: {bibtex_result.stderr}")

            # Second pass - resolve references
            logger.info(f"Starting second pass compilation for document {document_id}")
            result2 = subprocess.run([
                'pdflatex',
                '-output-directory', '.',
                '-interaction=nonstopmode',
                '-file-line-error',
                tex_basename
            ], capture_output=True, text=True, timeout=60, env=latex_env, cwd=temp_dir,
            encoding='utf-8', errors='replace')

            # Third pass - final compilation for complex references
            logger.info(f"Starting third pass compilation for document {document_id}")
            result3 = subprocess.run([
                'pdflatex',
                '-output-directory', '.',
                '-interaction=nonstopmode',
                '-file-line-error',
                tex_basename
            ], capture_output=True, text=True, timeout=60, env=latex_env, cwd=temp_dir,
            encoding='utf-8', errors='replace')

            # Check final result
            pdf_filename = os.path.join(temp_dir, f"document_{document_id}.pdf")

            if result3.returncode == 0 and os.path.exists(pdf_filename):
                logger.info(f"Successfully compiled document {document_id}")
                return True, []
            else:
                # Parse errors from all compilation attempts
                all_output = result1.stdout + result1.stderr + result2.stdout + result2.stderr + result3.stdout + result3.stderr
                errors = LatexService._parse_latex_errors(all_output)
                logger.error(f"Compilation failed for document {document_id}: {errors}")
                return False, errors

        except subprocess.TimeoutExpired:
            errors = ['Compilation timeout - document too complex. Consider simplifying or breaking into smaller sections.']
            return False, errors
        except Exception as e:
            logger.error(f"Multi-pass compilation error for document {document_id}: {e}")
            errors = [f'Compilation error: {str(e)}']
            return False, errors

    @staticmethod
    def _upload_pdf_to_storage(document_id: str, pdf_filename: str) -> str:
        """Upload PDF to Supabase storage and return public URL"""
        try:
            supabase = get_service_supabase()

            # Generate unique filename
            unique_filename = f"documents/{document_id}/{uuid.uuid4()}.pdf"

            # Read PDF file
            with open(pdf_filename, 'rb') as pdf_file:
                pdf_data = pdf_file.read()

            # Try to upload to Supabase storage
            try:
                result = supabase.storage.from_('documents').upload(
                    unique_filename,
                    pdf_data,
                    file_options={'content-type': 'application/pdf'}
                )

                if hasattr(result, 'error') and result.error:
                    logger.error(f"Error uploading PDF to storage: {result.error}")
                    # Fall back to local storage if Supabase storage fails
                    return LatexService._save_pdf_locally(document_id, pdf_filename)

                # Get public URL
                public_url = supabase.storage.from_('documents').get_public_url(unique_filename)
                if public_url.data and public_url.data.public_url:
                    logger.info(f"Generated Supabase PDF URL: {public_url.data.public_url}")
                    return public_url.data.public_url
                else:
                    logger.warning("Failed to get public URL from Supabase")
                    return None

            except Exception as storage_error:
                logger.warning(f"Supabase storage failed, falling back to local storage: {storage_error}")
                return LatexService._save_pdf_locally(document_id, pdf_filename)

        except Exception as e:
            logger.error(f"Error uploading PDF to storage: {e}")
            return None

    @staticmethod
    def _save_pdf_locally(document_id: str, pdf_filename: str) -> str:
        """Save PDF locally as fallback and return URL (DEPRECATED - use Supabase Storage)"""
        logger.warning("Using deprecated local PDF storage - should migrate to Supabase Storage")
        try:
            # Create local storage directory
            local_dir = os.path.join('uploads', 'pdfs', document_id)
            os.makedirs(local_dir, exist_ok=True)

            # Copy PDF to local storage
            local_filename = f"{uuid.uuid4()}.pdf"
            local_path = os.path.join(local_dir, local_filename)

            import shutil
            shutil.copy2(pdf_filename, local_path)

            # Return local URL (this would need to be served by the Flask app)
            # Make it a full URL for frontend consumption
            from flask import request
            base_url = request.url_root.rstrip('/') if request else 'http://localhost:5000'
            local_url = f"{base_url}/api/latex/pdf/{document_id}/{local_filename}"
            logger.info(f"Generated local PDF URL: {local_url}")
            return local_url

        except Exception as e:
            logger.error(f"Error saving PDF locally: {e}")
            return None
    
    @staticmethod
    def _parse_latex_errors(output: str) -> list:
        """Parse LaTeX compilation errors from output and provide helpful suggestions"""
        errors = []

        # Handle encoding issues in output
        try:
            if isinstance(output, bytes):
                output = output.decode('utf-8', errors='replace')
            lines = output.split('\n')
        except (UnicodeDecodeError, AttributeError):
            # If there are encoding issues, try to clean the output
            try:
                output = str(output).encode('utf-8', errors='replace').decode('utf-8')
                lines = output.split('\n')
            except:
                errors.append("Compilation error: 'utf-8' codec can't decode byte - document contains unsupported characters")
                return errors

        for i, line in enumerate(lines):
            # Look for error indicators
            if '! ' in line or 'Error:' in line or 'error:' in line:
                error_msg = line.strip()

                # Provide helpful suggestions for common errors
                if 'File `' in error_msg and 'not found' in error_msg:
                    filename = error_msg.split('`')[1].split("'")[0]
                    if filename.endswith('.sty'):
                        package_name = filename.replace('.sty', '')
                        error_msg = f"Missing LaTeX package: {package_name}. Please remove the \\usepackage{{{package_name}}} line or install the package on the server."
                    elif filename.endswith(('.png', '.jpg', '.jpeg', '.pdf', '.eps')):
                        error_msg = f"Missing image file: {filename}. Please ensure the image file exists or remove the \\includegraphics command."
                    else:
                        error_msg = f"Missing file: {filename}. Please check if the file exists and is accessible."
                elif 'Undefined control sequence' in error_msg:
                    error_msg += " - Check for typos in LaTeX commands or missing packages."
                elif 'Missing $' in error_msg:
                    error_msg += " - Math expressions need to be enclosed in $ signs."
                elif 'Extra }' in error_msg or 'Missing {' in error_msg:
                    error_msg += " - Check for balanced braces { } in your document."

                errors.append(error_msg)

        # If no specific errors found, return generic message
        if not errors:
            if 'Emergency stop' in output:
                errors.append('LaTeX compilation failed - check your syntax and ensure all packages are available')
            elif 'File `' in output and 'not found' in output:
                errors.append('Missing LaTeX packages. Try removing custom package imports or use basic LaTeX commands.')
            elif output.strip():
                errors.append('LaTeX compilation failed - please check your LaTeX syntax')
            else:
                errors.append('LaTeX compilation failed - no output generated')

        return errors[:3]  # Limit to first 3 errors for better readability
    
    @staticmethod
    def update_version_with_pdf_url(version_id: str, pdf_url: str):
        """Update document version with compiled PDF URL"""
        try:
            supabase = get_service_supabase()

            result = supabase.table('document_versions').update({
                'compiled_pdf_url': pdf_url
            }).eq('id', version_id).execute()

            return result.data[0] if result.data else None

        except Exception as e:
            logger.error(f"Error updating version with PDF URL: {e}")
            return None

    @staticmethod
    def _copy_referenced_images(latex_code: str, temp_dir: str, user_id: str):
        """Copy referenced images to the compilation directory and handle missing images"""
        print(f"=== STARTING IMAGE COPYING FOR USER {user_id} ===")
        logger.info(f"=== STARTING IMAGE COPYING FOR USER {user_id} ===")
        try:
            print("Importing services...")
            from services.file_service import FileService
            from services.folder_service import FolderService
            print("Services imported successfully")

            # Find all includegraphics commands
            image_pattern = r'\\includegraphics(?:\[[^\]]*\])?\{([^}]+)\}'
            matches = re.findall(image_pattern, latex_code)

            print(f"=== IMAGE REFERENCES ===")
            print(f"Found {len(matches)} image references in LaTeX code: {matches}")
            logger.info(f"Found {len(matches)} image references in LaTeX code: {matches}")

            # Get all user files and folders for efficient lookup
            print("Getting user files...")
            all_files = FileService.get_user_files(user_id)
            print(f"Got {len(all_files)} files")

            print("Getting user folders...")
            all_folders = FolderService.get_user_folders(user_id, 'all')
            print(f"Got {len(all_folders)} folders")

            logger.info(f"Found {len(all_files)} files and {len(all_folders)} folders for user {user_id}")

            # Create folder lookup maps
            folder_by_id = {f['id']: f for f in all_folders}

            # Debug: log folder structure
            print("=== FOLDER STRUCTURE ===")
            for folder in all_folders:
                print(f"Folder: {folder['name']} (ID: {folder['id']}, Parent: {folder.get('parent_folder_id')})")
                logger.info(f"Folder: {folder['name']} (ID: {folder['id']}, Parent: {folder.get('parent_folder_id')})")

            # Debug: log file structure
            print("=== FILE STRUCTURE ===")
            for file in all_files:
                print(f"File: {file['name']} (Type: {file.get('type')}, Folder: {file.get('folder_id')}, Path: {file.get('storage_path')})")
                logger.info(f"File: {file['name']} (Type: {file.get('type')}, Folder: {file.get('folder_id')}, Path: {file.get('storage_path')})")

            for image_path in matches:
                print(f"=== PROCESSING IMAGE: {image_path} ===")
                logger.info(f"Processing image reference: {image_path}")

                # Handle different path formats
                filename = None
                folder_names = []

                if image_path.startswith('@'):
                    # Handle @mention style references like @images/database.jpg
                    clean_path = image_path[1:]
                    path_parts = clean_path.split('/')
                    filename = path_parts[-1]
                    if len(path_parts) > 1:
                        folder_names = path_parts[:-1]
                elif '/' in image_path:
                    # Handle images/database.jpg style references
                    path_parts = image_path.split('/')
                    filename = path_parts[-1]
                    folder_names = path_parts[:-1]
                else:
                    # Handle direct filename references like database.jpg
                    filename = image_path

                if not filename:
                    continue

                # Find the target folder if folder path is specified
                target_folder_id = None
                if folder_names:
                    print(f"Looking for folder path: {folder_names}")
                    target_folder_id = LatexService._find_folder_by_path(folder_names, folder_by_id)
                    print(f"Found target folder ID: {target_folder_id}")
                else:
                    print("No folder path specified, looking in root")

                # Find the file
                print(f"Looking for file '{filename}' in folder ID: {target_folder_id}")
                logger.info(f"Looking for file '{filename}' in folder ID: {target_folder_id}")
                matching_file = LatexService._find_file_in_folder(filename, target_folder_id, all_files)
                print(f"Found matching file: {matching_file['name'] if matching_file else 'None'}")

                if matching_file and matching_file.get('storage_path'):
                    storage_path = matching_file['storage_path']
                    print(f"Storage path: {storage_path}")

                    # Check if it's a Supabase Storage path or local path
                    if storage_path.startswith('users/') and 'files/' in storage_path:
                        # Supabase Storage path
                        print(f"Downloading from Supabase Storage: {storage_path}")
                        try:
                            supabase = get_service_supabase()
                            result = supabase.storage.from_('files').download(storage_path)

                            if hasattr(result, 'error') and result.error:
                                print(f"Error downloading from Supabase: {result.error}")
                                logger.warning(f"Failed to download image from Supabase Storage: {result.error}")
                                LatexService._create_placeholder_image(temp_dir, image_path)
                                continue

                            # Create the directory structure in temp_dir to match LaTeX expectations
                            if folder_names:
                                dest_dir = os.path.join(temp_dir, *folder_names)
                                print(f"Creating directory: {dest_dir}")
                                os.makedirs(dest_dir, exist_ok=True)
                                dest_path = os.path.join(dest_dir, filename)
                            else:
                                dest_path = os.path.join(temp_dir, filename)

                            # Write downloaded data to file
                            with open(dest_path, 'wb') as f:
                                f.write(result.data)

                            print(f"Successfully downloaded and saved image {filename}")
                            logger.info(f"Downloaded image {filename} from Supabase Storage to {dest_path}")

                        except Exception as e:
                            print(f"Error downloading from Supabase Storage: {e}")
                            logger.error(f"Error downloading image from Supabase Storage: {e}")
                            LatexService._create_placeholder_image(temp_dir, image_path)

                    elif os.path.exists(storage_path):
                        # Local file path (legacy)
                        print(f"Using local file: {storage_path}")
                        logger.warning("Using legacy local file storage - should migrate to Supabase Storage")

                        # Create the directory structure in temp_dir to match LaTeX expectations
                        if folder_names:
                            dest_dir = os.path.join(temp_dir, *folder_names)
                            print(f"Creating directory: {dest_dir}")
                            os.makedirs(dest_dir, exist_ok=True)
                            dest_path = os.path.join(dest_dir, filename)
                        else:
                            dest_path = os.path.join(temp_dir, filename)

                        print(f"Copying from {storage_path} to {dest_path}")
                        shutil.copy2(storage_path, dest_path)
                        print(f"Successfully copied image {filename}")
                        logger.info(f"Copied image {filename} from local storage to {dest_path}")
                    else:
                        print(f"Storage path does not exist: {storage_path}")
                        logger.warning(f"Image file not found at storage path: {storage_path}")
                        LatexService._create_placeholder_image(temp_dir, image_path)
                else:
                    print(f"No matching file found or no storage path")
                    logger.warning(f"Referenced image not found: {image_path}")
                    LatexService._create_placeholder_image(temp_dir, image_path)

        except Exception as e:
            print(f"ERROR in image copying: {e}")
            logger.error(f"Error copying referenced images: {e}")
            import traceback
            print("Full traceback:")
            traceback.print_exc()
            # Don't fail compilation if image copying fails

    @staticmethod
    def _find_folder_by_path(folder_names: list, folder_by_id: dict):
        """Find folder ID by traversing the folder path"""
        try:
            current_folder_id = None

            for folder_name in folder_names:
                # Find folder with this name that has the current folder as parent
                found_folder = None

                # Search all folders for one with matching name and parent
                for folder in folder_by_id.values():
                    if (folder['name'] == folder_name and
                        folder.get('parent_folder_id') == current_folder_id):
                        found_folder = folder
                        break

                if found_folder:
                    current_folder_id = found_folder['id']
                    logger.info(f"Found folder '{folder_name}' with ID: {current_folder_id}")
                else:
                    logger.warning(f"Folder not found in path: {folder_name}")
                    return None

            return current_folder_id

        except Exception as e:
            logger.error(f"Error finding folder by path: {e}")
            return None

    @staticmethod
    def _find_file_in_folder(filename: str, folder_id: str, all_files: list):
        """Find a file by name in a specific folder (or root if folder_id is None)"""
        try:
            for file_record in all_files:
                if file_record['name'] == filename:
                    # Check if file is in the target folder
                    if folder_id is None:
                        # Looking for file in root (no folder)
                        if file_record.get('folder_id') is None:
                            return file_record
                    else:
                        # Looking for file in specific folder
                        if file_record.get('folder_id') == folder_id:
                            return file_record

            # If not found in specific folder, try any folder as fallback
            for file_record in all_files:
                if file_record['name'] == filename:
                    logger.warning(f"Found {filename} in different folder than expected")
                    return file_record

            return None

        except Exception as e:
            logger.error(f"Error finding file in folder: {e}")
            return None

    @staticmethod
    def _create_placeholder_image(temp_dir: str, image_path: str):
        """Create a placeholder image for missing images"""
        try:
            # Create directory structure if needed
            if '/' in image_path:
                dest_dir = os.path.dirname(os.path.join(temp_dir, image_path))
                os.makedirs(dest_dir, exist_ok=True)
                dest_path = os.path.join(temp_dir, image_path)
            else:
                dest_path = os.path.join(temp_dir, image_path)

            # Create a simple placeholder image using ImageMagick or PIL if available
            # For now, create a simple text file that LaTeX can handle
            placeholder_content = f"""% Placeholder for missing image: {image_path}
% This file was created because the referenced image was not found
"""

            # Try to create a minimal image file
            try:
                # Create a simple 1x1 pixel image using convert (ImageMagick)
                subprocess.run([
                    'convert', '-size', '100x100', 'xc:lightgray',
                    '-pointsize', '12', '-gravity', 'center',
                    '-annotate', '0', f'Missing\\n{os.path.basename(image_path)}',
                    dest_path
                ], capture_output=True, timeout=10)
                logger.info(f"Created placeholder image: {dest_path}")
            except (subprocess.TimeoutExpired, FileNotFoundError):
                # If ImageMagick is not available, create a simple text file
                # and let LaTeX handle the missing image gracefully
                logger.warning(f"Could not create placeholder image for {image_path}, LaTeX will handle missing image")

        except Exception as e:
            logger.error(f"Error creating placeholder image: {e}")

    @staticmethod
    def _enhance_latex_for_missing_images(latex_code: str) -> str:
        """Enhance LaTeX code to handle missing images gracefully and add common packages"""
        try:
            # Add packages for better image handling and common packages
            # Only add packages that are commonly available
            packages_to_add = [
                "\\usepackage{graphicx}",
                "\\usepackage{amsmath}",  # Essential math package
                "\\usepackage{amsfonts}",  # Math fonts
                "\\usepackage{amssymb}",  # Math symbols
                "\\DeclareGraphicsExtensions{.pdf,.png,.jpg,.jpeg,.gif}",
                "\\graphicspath{{./}{images/}{figures/}}"  # Search multiple paths
            ]

            # Check if document already has these packages
            enhanced_code = latex_code

            # Find the document class line
            doc_class_pattern = r'\\documentclass(?:\[[^\]]*\])?\{[^}]+\}'
            doc_class_match = re.search(doc_class_pattern, enhanced_code)

            if doc_class_match:
                # Insert packages after documentclass
                insert_pos = doc_class_match.end()

                # Check which packages are already present
                packages_needed = []
                for package in packages_to_add:
                    if package not in enhanced_code:
                        packages_needed.append(package)

                if packages_needed:
                    package_block = "\n" + "\n".join(packages_needed) + "\n"
                    enhanced_code = enhanced_code[:insert_pos] + package_block + enhanced_code[insert_pos:]

            # Wrap includegraphics commands with error handling
            def replace_includegraphics(match):
                options = match.group(1) if match.group(1) else ""
                filename = match.group(2)

                # Create a more robust includegraphics command
                return f"""\\IfFileExists{{{filename}}}{{%
    \\includegraphics{options}{{{filename}}}%
}}{{%
    \\fbox{{\\parbox{{5cm}}{{\\centering Missing Image:\\\\\\texttt{{{filename}}}}}}}%
}}"""

            # Replace includegraphics commands
            includegraphics_pattern = r'\\includegraphics(\[[^\]]*\])?\{([^}]+)\}'
            enhanced_code = re.sub(includegraphics_pattern, replace_includegraphics, enhanced_code)

            return enhanced_code

        except Exception as e:
            logger.error(f"Error enhancing LaTeX for missing images: {e}")
            return latex_code  # Return original if enhancement fails

    @staticmethod
    def _remove_problematic_packages(latex_code: str) -> str:
        """Remove or replace packages that are commonly missing or problematic"""
        try:
            # Check if document has bibliography content
            has_bibliography = LatexService._has_bibliography_content(latex_code)
            print(f"=== BIBLIOGRAPHY CHECK ===")
            print(f"Has bibliography content: {has_bibliography}")

            # List of packages that are often missing and their replacements
            # Be more conservative - only remove packages that are definitely problematic
            problematic_packages = {
                'algorithm2e': '',  # Remove entirely (conflicts with algorithm+algpseudocode)
                'listings': '',  # Remove entirely for now (can cause issues)
                'minted': '',  # Remove entirely (requires Python)
                'tikz': '',  # Remove entirely (complex package, often missing)
                'pgfplots': '',  # Remove entirely (complex package, often missing)
                'pgfkeys': '',  # Remove entirely (causes errors)
                'pgf': '',  # Remove entirely (complex package)
                'tcolorbox': '',  # Remove temporarily due to pgfkeys errors
                'xcolor': '',  # Remove temporarily due to color errors
                'titlesec': '',  # Remove temporarily due to titlesec errors
            }

            # Add natbib to problematic packages if no bibliography content
            if not has_bibliography:
                print("No bibliography content found - adding bibliography packages to removal list")
                problematic_packages['natbib'] = ''  # Remove natbib if no bibliography
                problematic_packages['biblatex'] = ''  # Remove biblatex if no bibliography
                problematic_packages['cite'] = ''  # Remove cite if no bibliography
            else:
                print("Bibliography content found - keeping bibliography packages")

            enhanced_code = latex_code
            print(f"=== PACKAGE REMOVAL ===")
            print(f"Packages to remove: {list(problematic_packages.keys())}")

            for package, replacement in problematic_packages.items():
                # Remove usepackage lines for problematic packages
                pattern = rf'\\usepackage(?:\[[^\]]*\])?\{{{package}\}}'
                if re.search(pattern, enhanced_code):
                    print(f"Found and removing package: {package}")
                    if replacement:
                        enhanced_code = re.sub(pattern, replacement, enhanced_code)
                    else:
                        enhanced_code = re.sub(pattern, f'% Removed problematic package: {package}', enhanced_code)
                else:
                    print(f"Package {package} not found in document")

            # If we removed bibliography packages, also remove bibliography commands
            if not has_bibliography:
                print("Removing bibliography commands...")
                enhanced_code = LatexService._remove_bibliography_commands(enhanced_code)

            # Also remove specific environments that might cause issues
            # Be much more careful with regex patterns to avoid corrupting valid LaTeX

            # Remove tcolorbox environments (with optional parameters)
            enhanced_code = re.sub(
                r'\\begin\{tcolorbox\}(?:\[[^\]]*\])?\s*.*?\s*\\end\{tcolorbox\}',
                '% tcolorbox environment removed due to pgfkeys errors',
                enhanced_code, flags=re.DOTALL
            )

            # Remove tcbset commands (handle nested braces properly)
            # This is a complex multi-line command, so we need a more robust approach
            def remove_tcbset(text):
                result = []
                lines = text.split('\n')
                i = 0
                while i < len(lines):
                    line = lines[i]
                    if '\\tcbset{' in line:
                        # Found start of tcbset, now find the matching closing brace
                        result.append('% tcbset command removed')
                        brace_count = line.count('{') - line.count('}')
                        i += 1
                        while i < len(lines) and brace_count > 0:
                            brace_count += lines[i].count('{') - lines[i].count('}')
                            i += 1
                    else:
                        result.append(line)
                        i += 1
                return '\n'.join(result)

            enhanced_code = remove_tcbset(enhanced_code)

            # Remove definecolor commands (be very specific)
            enhanced_code = re.sub(
                r'\\definecolor\s*\{[^}]+\}\s*\{[^}]+\}\s*\{[^}]+\}',
                '% definecolor command removed',
                enhanced_code
            )

            # Remove titlesec commands
            enhanced_code = re.sub(
                r'\\titleformat\s*\{[^}]+\}.*?(?=\\[a-zA-Z]|\n\n|\Z)',
                '% titleformat command removed',
                enhanced_code, flags=re.DOTALL
            )

            enhanced_code = re.sub(
                r'\\titlespacing\*?\s*\{[^}]+\}.*?(?=\\[a-zA-Z]|\n\n|\Z)',
                '% titlespacing command removed',
                enhanced_code, flags=re.DOTALL
            )

            # Fix orphaned \end{center} commands (when \begin{center} was in a removed tcolorbox)
            enhanced_code = re.sub(
                r'\\end\{center\}(?!\s*\\end\{tcolorbox\})',
                '% orphaned end{center} removed',
                enhanced_code
            )

            # Fix orphaned \begin{center} commands
            enhanced_code = re.sub(
                r'\\begin\{center\}(?=.*?% tcolorbox environment removed)',
                '% orphaned begin{center} removed',
                enhanced_code, flags=re.DOTALL
            )

            return enhanced_code

        except Exception as e:
            logger.error(f"Error removing problematic packages: {e}")
            return latex_code  # Return original if processing fails

    @staticmethod
    def _has_bibliography_content(latex_code: str) -> bool:
        """Check if the LaTeX document has bibliography content"""
        try:
            # Split document into preamble and body
            document_start = latex_code.find('\\begin{document}')
            if document_start == -1:
                # No document environment found, check entire content
                document_body = latex_code
            else:
                # Only check the document body for citations
                document_body = latex_code[document_start:]

            # Check for bibliography commands in the document body
            bibliography_patterns = [
                r'\\bibliography\{[^}]+\}',  # \bibliography{filename}
                r'\\bibliographystyle\{[^}]+\}',  # \bibliographystyle{style}
                r'\\cite\{[^}]+\}',  # \cite{key} - only in document body
                r'\\citep\{[^}]+\}',  # \citep{key} (natbib)
                r'\\citet\{[^}]+\}',  # \citet{key} (natbib)
                r'\\nocite\{[^}]+\}',  # \nocite{key}
                r'\\begin\{thebibliography\}',  # manual bibliography
                r'\\bibitem\{[^}]+\}',  # bibliography items
                r'\\addbibresource\{[^}]+\}',  # biblatex
                r'\\printbibliography',  # biblatex
            ]

            for pattern in bibliography_patterns:
                matches = re.findall(pattern, document_body)
                if matches:
                    print(f"Found bibliography pattern '{pattern}': {matches}")
                    return True

            print("No bibliography content found in document body")
            return False

        except Exception as e:
            logger.error(f"Error checking bibliography content: {e}")
            return False  # Assume no bibliography if error

    @staticmethod
    def _remove_bibliography_commands(latex_code: str) -> str:
        """Remove bibliography-related commands when no bibliography is present"""
        try:
            enhanced_code = latex_code

            # Remove bibliography commands that would cause errors without natbib
            bibliography_commands = [
                r'\\bibliography\{[^}]+\}',  # \bibliography{filename}
                r'\\bibliographystyle\{[^}]+\}',  # \bibliographystyle{style}
                r'\\cite\{[^}]+\}',  # \cite{key}
                r'\\citep\{[^}]+\}',  # \citep{key} (natbib)
                r'\\citet\{[^}]+\}',  # \citet{key} (natbib)
                r'\\nocite\{[^}]+\}',  # \nocite{key}
                r'\\addbibresource\{[^}]+\}',  # biblatex
                r'\\printbibliography',  # biblatex
            ]

            for pattern in bibliography_commands:
                enhanced_code = re.sub(pattern, '% Removed bibliography command (no bibliography present)', enhanced_code)

            # Remove thebibliography environment
            enhanced_code = re.sub(
                r'\\begin\{thebibliography\}.*?\\end\{thebibliography\}',
                '% Removed thebibliography environment',
                enhanced_code, flags=re.DOTALL
            )

            return enhanced_code

        except Exception as e:
            logger.error(f"Error removing bibliography commands: {e}")
            return latex_code  # Return original if processing fails

    @staticmethod
    def _clean_latex_encoding(latex_code: str) -> str:
        """Clean LaTeX code of problematic characters and encoding issues"""
        try:
            # Remove or replace problematic characters
            cleaned_code = latex_code

            # Replace common problematic characters
            replacements = {
                '\u2013': '--',  # en dash
                '\u2014': '---',  # em dash
                '\u2018': '`',    # left single quote
                '\u2019': "'",    # right single quote
                '\u201c': '``',   # left double quote
                '\u201d': "''",   # right double quote
                '\u2026': '...',  # ellipsis
                '\u00a0': ' ',    # non-breaking space
                '\u00e9': 'e',    # é
                '\u00e8': 'e',    # è
                '\u00ea': 'e',    # ê
                '\u00e0': 'a',    # à
                '\u00e7': 'c',    # ç
                '\u00f1': 'n',    # ñ
            }

            for old_char, new_char in replacements.items():
                cleaned_code = cleaned_code.replace(old_char, new_char)

            # Remove any remaining non-ASCII characters that might cause issues
            # DISABLED: This was too aggressive and corrupting valid LaTeX
            # cleaned_code = ''.join(char if ord(char) < 128 else '?' for char in cleaned_code)

            # Ensure proper LaTeX encoding declaration
            if '\\usepackage[utf8]' not in cleaned_code and '\\documentclass' in cleaned_code:
                # Add UTF-8 input encoding after documentclass
                doc_class_pattern = r'(\\documentclass(?:\[[^\]]*\])?\{[^}]+\})'
                cleaned_code = re.sub(doc_class_pattern, r'\1\n\\usepackage[utf8]{inputenc}\n\\usepackage[T1]{fontenc}', cleaned_code)

            return cleaned_code

        except Exception as e:
            logger.error(f"Error cleaning LaTeX encoding: {e}")
            # Return original code instead of corrupting it
            return latex_code

    @staticmethod
    def _validate_and_fix_latex_structure(latex_code: str) -> str:
        """Validate and fix basic LaTeX document structure"""
        try:
            # Clean the LaTeX code first
            cleaned_code = LatexService._clean_latex_encoding(latex_code)

            # Check for basic document structure
            if not cleaned_code.strip():
                return LatexService._create_minimal_document("Empty document")

            # Check for documentclass
            if '\\documentclass' not in cleaned_code:
                logger.warning("No documentclass found, creating minimal document")
                return LatexService._create_minimal_document(cleaned_code)

            # Check for begin{document} and end{document}
            if '\\begin{document}' not in cleaned_code:
                logger.warning("No \\begin{document} found, fixing structure")
                cleaned_code = LatexService._fix_document_structure(cleaned_code)

            if '\\end{document}' not in cleaned_code:
                logger.warning("No \\end{document} found, adding it")
                cleaned_code += '\n\\end{document}\n'

            # Fix common LaTeX command issues
            cleaned_code = LatexService._fix_common_latex_issues(cleaned_code)

            return cleaned_code

        except Exception as e:
            logger.error(f"Error validating LaTeX structure: {e}")
            # Return a minimal working document as fallback
            return LatexService._create_minimal_document("Error in document structure")

    @staticmethod
    def _create_minimal_document(content: str) -> str:
        """Create a minimal working LaTeX document"""
        return f"""\\documentclass{{article}}
\\usepackage[utf8]{{inputenc}}
\\usepackage[T1]{{fontenc}}
\\usepackage{{amsmath}}
\\usepackage{{amsfonts}}
\\usepackage{{amssymb}}
\\usepackage{{graphicx}}

\\begin{{document}}

{content}

\\end{{document}}
"""

    @staticmethod
    def _fix_document_structure(latex_code: str) -> str:
        """Fix basic document structure issues"""
        try:
            # Find documentclass
            doc_class_match = re.search(r'\\documentclass(?:\[[^\]]*\])?\{[^}]+\}', latex_code)
            if not doc_class_match:
                return LatexService._create_minimal_document(latex_code)

            # Split into preamble and content
            doc_class_end = doc_class_match.end()
            preamble_part = latex_code[:doc_class_end]
            remaining_part = latex_code[doc_class_end:]

            # Look for existing packages and content
            begin_doc_match = re.search(r'\\begin\{document\}', remaining_part)
            if begin_doc_match:
                # Document structure exists, just clean it
                return latex_code
            else:
                # Add begin{document} and structure
                # Find where packages end (look for first non-package, non-comment line)
                lines = remaining_part.split('\n')
                preamble_lines = []
                content_lines = []
                in_content = False

                for line in lines:
                    stripped = line.strip()
                    if not stripped or stripped.startswith('%'):
                        # Empty line or comment
                        if in_content:
                            content_lines.append(line)
                        else:
                            preamble_lines.append(line)
                    elif stripped.startswith('\\usepackage') or stripped.startswith('\\newcommand') or stripped.startswith('\\def'):
                        # Preamble content
                        preamble_lines.append(line)
                    else:
                        # Content
                        in_content = True
                        content_lines.append(line)

                # Reconstruct document
                result = preamble_part + '\n' + '\n'.join(preamble_lines)
                result += '\n\n\\begin{document}\n\n'
                result += '\n'.join(content_lines)

                return result

        except Exception as e:
            logger.error(f"Error fixing document structure: {e}")
            return LatexService._create_minimal_document(latex_code)

    @staticmethod
    def _fix_common_latex_issues(latex_code: str) -> str:
        """Fix common LaTeX syntax issues"""
        try:
            fixed_code = latex_code

            # Fix incomplete commands (like truncated \\begin)
            # Look for incomplete \\be, \\en, etc.
            incomplete_patterns = [
                (r'\\be(?![a-zA-Z])', r'\\begin'),  # \\be -> \\begin
                (r'\\b(?![a-zA-Z])', r'\\begin'),   # \\b -> \\begin (very truncated)
                (r'\\en(?![a-zA-Z])', r'\\end'),    # \\en -> \\end
                (r'\\e(?![a-zA-Z])', r'\\end'),     # \\e -> \\end (very truncated)
                (r'\\sec(?![a-zA-Z])', r'\\section'),  # \\sec -> \\section
                (r'\\sub(?![a-zA-Z])', r'\\subsection'),  # \\sub -> \\subsection
                (r'\\use(?![a-zA-Z])', r'\\usepackage'),  # \\use -> \\usepackage
                (r'\\doc(?![a-zA-Z])', r'\\documentclass'),  # \\doc -> \\documentclass
            ]

            for pattern, replacement in incomplete_patterns:
                fixed_code = re.sub(pattern, replacement, fixed_code)

            # More aggressive fix for very truncated commands - disabled for now
            # This was too aggressive and was removing valid LaTeX commands
            # very_truncated_patterns = [
            #     (r'(^|\s)\\([a-z]{1,2})(\s|$)', r'\1% Removed truncated command: \\\2\3'),
            # ]
            #
            # for pattern, replacement in very_truncated_patterns:
            #     fixed_code = re.sub(pattern, replacement, fixed_code, flags=re.MULTILINE)

            # Fix unmatched braces (basic fix)
            # Count braces and add missing ones at the end
            open_braces = fixed_code.count('{')
            close_braces = fixed_code.count('}')

            if open_braces > close_braces:
                fixed_code += '}' * (open_braces - close_braces)
            elif close_braces > open_braces:
                # Remove extra closing braces (simple approach)
                extra_closes = close_braces - open_braces
                for _ in range(extra_closes):
                    # Remove the last standalone }
                    fixed_code = re.sub(r'\}(?=[^}]*$)', '', fixed_code, count=1)

            # Fix common environment issues
            # Ensure environments are properly closed
            begin_pattern = r'\\begin\{([^}]+)\}'
            begins = re.findall(begin_pattern, fixed_code)

            for env in begins:
                end_pattern = f'\\\\end\\{{{env}\\}}'
                if not re.search(end_pattern, fixed_code):
                    # Add missing \\end{env}
                    fixed_code += f'\n\\end{{{env}}}\n'

            return fixed_code

        except Exception as e:
            logger.error(f"Error fixing common LaTeX issues: {e}")
            return latex_code
