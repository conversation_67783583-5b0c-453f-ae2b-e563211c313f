# DeepDocX Backend

A comprehensive Flask backend for the DeepDocX LaTeX document management system with AI integration.

## Features

- **Authentication**: Email/password + OAuth (Google, GitHub)
- **Document Management**: LaTeX document creation, versioning, and AI-powered editing
- **File Management**: Upload/download files with folder organization and content extraction
- **AI Integration**: OpenAI-powered LaTeX generation and enhancement
- **Agentic Document Creation**: Multi-agent system for intelligent document planning and creation
- **File Content Extraction**: Automatic extraction from PDFs, DOCX, CSV, Excel files
- **Document Type Classification**: Automatic detection of document types (research papers, invoices, exams, etc.)
- **Intelligent Planning**: Context-aware document structure and content planning
- **Real-time Collaboration**: Document sharing with permissions
- **Supabase Integration**: PostgreSQL database with Row Level Security

## Quick Start

### 1. Prerequisites

- Python 3.8+
- Supabase account
- OpenAI API key
- Google OAuth credentials (optional)
- GitHub OAuth credentials (optional)

### 2. Installation

```bash
# Clone and navigate to backend directory
cd backend-deepdocx

# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt
```

### 3. Database Setup

1. Create a new Supabase project at [supabase.com](https://supabase.com)
2. Run the SQL commands from `database_schema.sql` in your Supabase SQL editor
3. Note your Supabase URL and API keys

### 4. Environment Configuration

```bash
# Copy environment template
cp .env.example .env

# Edit .env with your actual values
nano .env
```

Required environment variables:
- `SUPABASE_URL`: Your Supabase project URL
- `SUPABASE_KEY`: Your Supabase anon key
- `SUPABASE_SERVICE_ROLE_KEY`: Your Supabase service role key
- `OPENAI_API_KEY`: Your OpenAI API key
- `SECRET_KEY`: Flask secret key (generate a secure random string)
- `JWT_SECRET_KEY`: JWT secret key (generate a secure random string)

### 5. OAuth Setup (Optional)

#### Google OAuth:
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing
3. Enable Google+ API
4. Create OAuth 2.0 credentials
5. Add redirect URI: `http://localhost:5000/api/auth/google/callback`
6. Add client ID and secret to `.env`

#### GitHub OAuth:
1. Go to GitHub Settings > Developer settings > OAuth Apps
2. Create a new OAuth App
3. Set Authorization callback URL: `http://localhost:5000/api/auth/github/callback`
4. Add client ID and secret to `.env`

### 6. Run the Application

```bash
# Development mode
python app.py

# Or with Flask CLI
export FLASK_APP=app.py
export FLASK_ENV=development
flask run --host=0.0.0.0 --port=5000
```

The API will be available at `http://localhost:5000`

## API Endpoints

### Authentication
- `POST /api/auth/signup` - User registration
- `POST /api/auth/signin` - User login
- `POST /api/auth/signout` - User logout
- `GET /api/auth/me` - Get current user
- `GET /api/auth/google` - Initiate Google OAuth
- `GET /api/auth/github` - Initiate GitHub OAuth

### Documents
- `GET /api/documents` - List user documents
- `POST /api/documents` - Create new document
- `GET /api/documents/{id}` - Get document details
- `PATCH /api/documents/{id}` - Update document
- `DELETE /api/documents/{id}` - Delete document
- `POST /api/documents/{id}/chat` - Process AI chat message
- `GET /api/documents/{id}/versions` - Get document versions
- `POST /api/documents/{id}/versions` - Create new version

### Files
- `GET /api/files` - List user files
- `POST /api/files/upload` - Upload file
- `GET /api/files/{id}` - Get file metadata
- `GET /api/files/{id}/content` - Get file content (text files)
- `DELETE /api/files/{id}` - Delete file

### Folders
- `GET /api/folders` - List folders
- `POST /api/folders` - Create folder
- `GET /api/folders/{id}/contents` - Get folder contents
- `PATCH /api/folders/{id}` - Update folder
- `DELETE /api/folders/{id}` - Delete folder

### Users
- `GET /api/users/profile` - Get user profile
- `PATCH /api/users/profile` - Update profile
- `GET /api/users/settings` - Get user settings
- `PATCH /api/users/settings` - Update settings
- `POST /api/users/change-password` - Change password

### Sharing
- `POST /api/share` - Create document share
- `GET /api/share/{token}` - Access shared document
- `GET /api/share/document/{id}` - List document shares
- `DELETE /api/share/{id}` - Revoke share

### Agent-Based Document Processing
- `POST /api/agents/process-document` - Process document creation/modification using agent system
- `GET /api/agents/files-with-content` - Get files that have extracted content available
- `GET /api/agents/file-content/{file_id}` - Get extracted content for a specific file

## Document Creation Workflow

### Traditional Workflow
1. **User sends first message**: Creates document and conversation
2. **AI generates title**: Based on user request
3. **AI creates LaTeX**: Initial document structure
4. **Version tracking**: Each AI response creates new version
5. **Conversation history**: All messages stored for context
6. **File references**: Support for @file.pdf, @image.png mentions

### New Agentic Workflow
1. **Document Classification**: AI agents automatically identify document type (research paper, invoice, exam, etc.)
2. **Content Extraction**: Referenced files are automatically processed to extract text, tables, and metadata
3. **Intelligent Planning**: Agents create detailed document structure based on type and requirements
4. **Clarification Phase**: Agents ask targeted questions if more information is needed
5. **Content Generation**: AI-powered writing agents create comprehensive, detailed content with substantial depth
6. **Quality Review**: Review agents ensure document quality and completeness
7. **Adaptive Length**: Documents automatically scale to appropriate length with comprehensive content (2-3 pages for invoices, 10-50+ pages for research papers)
8. **AI-Enhanced Writing**: Advanced content generation using OpenAI for creating detailed, professional-quality sections

## File Reference System

Users can reference files in their messages:
- `@image.png` - Include image in LaTeX
- `@paper.pdf` - Add to bibliography
- `@data.csv` - Reference data file

The AI will automatically incorporate these references into the LaTeX code.

## Development

### Project Structure
```
backend-deepdocx/
├── app.py                 # Flask application factory
├── requirements.txt       # Python dependencies
├── database_schema.sql    # Supabase database schema
├── .env.example          # Environment template
├── test_agents.py        # Agent system test script
├── services/             # Business logic
│   ├── auth_service.py   # Authentication
│   ├── document_service.py # Document management
│   ├── ai_service.py     # AI integration (enhanced with agents)
│   ├── file_service.py   # File handling (with content extraction)
│   ├── folder_service.py # Folder management
│   ├── file_extraction_service.py # File content extraction
│   ├── document_classifier.py # Document type classification
│   ├── document_planner.py # Document structure planning
│   ├── agent_orchestrator.py # Multi-agent coordination
│   ├── comprehensive_writers.py # Specialized document writers
│   └── ai_content_generator.py # AI-powered content generation
└── routes/               # API endpoints
    ├── auth.py           # Auth routes
    ├── documents.py      # Document routes
    ├── files.py          # File routes
    ├── folders.py        # Folder routes
    ├── users.py          # User routes
    ├── share.py          # Sharing routes
    └── agents.py         # Agent-based processing routes
```

## Agent System Architecture

The new agentic document creation system consists of multiple specialized AI agents:

### Core Agents

1. **Document Classifier Agent**
   - Automatically identifies document type from user requests
   - Supports: Research Papers, Business Reports, Invoices, Exams, Letters, Theses, Presentations
   - Considers file references and context clues

2. **Document Planner Agent**
   - Creates detailed document structure and outline
   - Estimates appropriate length and complexity
   - Defines section requirements and content types

3. **Clarification Agent**
   - Identifies missing information needed for optimal document creation
   - Asks targeted, context-aware questions
   - Manages user response collection and validation

4. **Writer Agent**
   - Generates comprehensive, detailed LaTeX content using advanced AI
   - Creates substantial sections with 500-2000+ words per section
   - Adapts writing style based on document type and requirements
   - Incorporates extracted file content intelligently
   - Uses OpenAI for professional-quality content generation

5. **Review Agent**
   - Quality assurance and document improvement
   - Ensures completeness and consistency
   - Applies final optimizations

### File Content Extraction

The system automatically extracts and processes content from uploaded files:

- **PDF Files**: Text extraction with table detection
- **DOCX Files**: Text and table extraction
- **CSV/Excel Files**: Data parsing with summary statistics
- **Text Files**: Direct content reading with encoding detection

### AI-Powered Content Generation

The system uses advanced AI to generate comprehensive, detailed content:

**Content Quality Features:**
- **Substantial Length**: Sections contain 500-2000+ words with detailed analysis
- **Professional Tone**: Academic and business-appropriate writing styles
- **Structured Content**: Proper use of subsections, lists, and LaTeX formatting
- **Context Integration**: Incorporates file content and user requirements
- **Domain Expertise**: Adapts content to specific fields (healthcare, technology, business, etc.)

**Research Paper Content:**
- Comprehensive literature reviews with multiple citations
- Detailed methodology sections with specific procedures
- Extensive results sections with statistical analysis
- In-depth discussion with theoretical and practical implications
- Substantial conclusions with future research directions

**Business Report Content:**
- Executive summaries with strategic insights
- Comprehensive market and performance analysis
- Detailed recommendations with implementation strategies
- Data-driven insights with charts and tables
- Professional business language and formatting

### Document Type Specifications

Each document type has specific characteristics:

- **Research Papers**: 10-50 pages, technical depth, citations required
- **Business Reports**: 5-20 pages, analytical focus, charts/graphs
- **Invoices**: 1-3 pages, table-based, formal structure
- **Exams**: 2-10 pages, question formats, clear instructions
- **Letters**: 1-3 pages, formal/informal tone options
- **Theses**: 50-300 pages, comprehensive structure, extensive citations

### Testing the Agent System

```bash
# Test the agent system components
python test_agents.py

# This will test:
# - Document classification
# - Document planning
# - File content extraction
# - Agent orchestration
```

### Testing

```bash
# Install test dependencies
pip install pytest pytest-flask

# Run tests
pytest

# Run with coverage
pytest --cov=.
```

### Deployment

For production deployment:

1. Set `FLASK_ENV=production`
2. Use a production WSGI server (gunicorn, uWSGI)
3. Set up proper logging
4. Configure HTTPS
5. Set secure secret keys
6. Configure Supabase RLS policies

```bash
# Example with gunicorn
pip install gunicorn
gunicorn -w 4 -b 0.0.0.0:5000 app:app
```

## Security Features

- JWT-based authentication
- Row Level Security (RLS) in Supabase
- File type validation
- Rate limiting ready
- CORS configuration
- OAuth state validation
- Secure file uploads

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

MIT License - see LICENSE file for details
