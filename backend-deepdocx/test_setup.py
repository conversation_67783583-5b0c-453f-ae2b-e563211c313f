#!/usr/bin/env python3
"""
Simple test script to verify backend setup
"""

import os
import sys
import requests
from dotenv import load_dotenv

def test_environment():
    """Test environment variables"""
    print("🔍 Testing environment variables...")
    
    load_dotenv()
    
    required_vars = [
        'SUPABASE_URL',
        'SUPABASE_KEY',
        'OPENAI_API_KEY',
        'SECRET_KEY',
        'JWT_SECRET_KEY'
    ]
    
    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print(f"❌ Missing environment variables: {', '.join(missing_vars)}")
        return False
    else:
        print("✅ All required environment variables are set")
        return True

def test_dependencies():
    """Test if all dependencies are installed"""
    print("\n🔍 Testing dependencies...")
    
    try:
        import flask
        import supabase
        import openai
        import bcrypt
        import jwt
        print("✅ All core dependencies are installed")
        return True
    except ImportError as e:
        print(f"❌ Missing dependency: {e}")
        return False

def test_server():
    """Test if server starts and responds"""
    print("\n🔍 Testing server startup...")
    
    try:
        from app import create_app
        app = create_app()
        
        with app.test_client() as client:
            response = client.get('/api/health')
            if response.status_code == 200:
                print("✅ Server starts and health check passes")
                return True
            else:
                print(f"❌ Health check failed with status {response.status_code}")
                return False
    except Exception as e:
        print(f"❌ Server startup failed: {e}")
        return False

def test_supabase_connection():
    """Test Supabase connection"""
    print("\n🔍 Testing Supabase connection...")
    
    try:
        from services.supabase_client import get_service_supabase
        from app import create_app

        app = create_app()
        with app.app_context():
            supabase = get_service_supabase()
            # Try a simple query
            result = supabase.table('users').select('count').execute()
            print("✅ Supabase connection successful")
            return True
    except Exception as e:
        print(f"❌ Supabase connection failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 DeepDocX Backend Setup Test\n")
    
    tests = [
        test_environment,
        test_dependencies,
        test_server,
        test_supabase_connection
    ]
    
    results = []
    for test in tests:
        results.append(test())
    
    print("\n" + "="*50)
    if all(results):
        print("🎉 All tests passed! Backend is ready to use.")
        print("\nNext steps:")
        print("1. Run: python run.py")
        print("2. Visit: http://localhost:5000/api/health")
        print("3. Test authentication endpoints")
        return 0
    else:
        print("❌ Some tests failed. Please fix the issues above.")
        return 1

if __name__ == '__main__':
    sys.exit(main())
