"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import {
  ArrowLeft,
  Plus,
  Clock,
  FolderOpen,
  FileText,
  Settings,
  LogOut,
  Search,
  Filter,
  Menu,
} from "lucide-react"
import { MobileMenu } from "../components/mobile-menu"
import { DocumentGrid } from "../components/document-grid"
import { SettingsDialog } from "@/components/settings-dialog"
import { useAuth } from "@/lib/contexts/auth-context"
import type { Document, AppState, DocumentOperations } from "../types"

interface DocumentsPageProps {
  documents: Document[]
  documentsLoading: boolean
  documentOperations: DocumentOperations
  onStateChange: (state: AppState) => void
  onDocumentClick: (doc: Document) => void
  onBackToWelcome: () => void
  onShowSettings: () => void
  isMobile: boolean
  mobileMenuOpen: boolean
  onToggleMobileMenu: () => void
  onCloseMobileMenu: () => void
}

export function DocumentsPage({
  documents,
  documentsLoading,
  documentOperations,
  onStateChange,
  onDocumentClick,
  onBackToWelcome,
  onShowSettings,
  isMobile,
  mobileMenuOpen,
  onToggleMobileMenu,
  onCloseMobileMenu,
}: DocumentsPageProps) {
  const [searchTerm, setSearchTerm] = useState("")
  const [showSettings, setShowSettings] = useState(false)
  const { user, signOut } = useAuth()

  const filteredDocuments = documents.filter((doc) =>
    doc.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    doc.description?.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const handleLogout = async () => {
    try {
      await signOut()
    } catch (error) {
      console.error("Logout failed:", error)
    }
  }

  const getUserInitials = () => {
    if (!user) return "U"
    return `${user.first_name?.[0] || ""}${user.last_name?.[0] || ""}`.toUpperCase()
  }

  const getUserDisplayName = () => {
    if (!user) return "User"
    return `${user.first_name || ""} ${user.last_name || ""}`.trim() || user.email
  }

  return (
    <div className="flex h-screen bg-background watercolor-wash">
      {/* Settings Dialog */}
      <SettingsDialog open={showSettings} onOpenChange={setShowSettings} />
      {/* Mobile Menu */}
      <MobileMenu
        isOpen={mobileMenuOpen}
        onClose={onCloseMobileMenu}
        currentPage="generated-documents"
        onNavigate={onStateChange}
        onBackToWelcome={onBackToWelcome}
        documents={documents}
        documentsLoading={documentsLoading}
        onDocumentClick={onDocumentClick}
        title="Documents Navigation Menu"
      />

      {/* Sidebar - Desktop */}
      <div className={`${isMobile ? "hidden" : "w-80"} bg-card border-r border-border shadow-lg flex flex-col`}>
        <div className="p-6 lg:p-8 border-b border-border">
          <div className="flex items-center gap-3">
            <Button
              variant="ghost"
              size="icon"
              onClick={onBackToWelcome}
              className="text-muted-foreground hover:text-primary"
            >
              <ArrowLeft className="w-5 h-5" />
            </Button>
            <img src="/logo.png" alt="deepdocx logo" className="w-32 h-auto" />
          </div>
        </div>

        {/* Navigation */}
        <div className="p-6 space-y-2">
          <Button
            onClick={() => onStateChange("welcome")}
            variant="ghost"
            className="w-full justify-start text-foreground hover:text-primary hover:bg-muted h-12"
          >
            <Plus className="w-5 h-5 mr-3" />
            New Document
          </Button>

          <Button className="w-full justify-start bg-primary hover:bg-primary/90 text-primary-foreground h-12">
            <Clock className="w-5 h-5 mr-3" />
            Generated Documents
          </Button>

          <Button
            onClick={() => onStateChange("files-management")}
            variant="ghost"
            className="w-full justify-start text-foreground hover:text-primary hover:bg-muted h-12"
          >
            <FolderOpen className="w-5 h-5 mr-3" />
            Files Management
          </Button>
        </div>

        {/* Recent Documents */}
        <div className="flex-1 px-6">
          <div className="mb-4">
            <h3 className="text-sm font-semibold text-foreground uppercase tracking-wider">Recent Documents</h3>
          </div>
          <ScrollArea className="space-y-2">
            {documents.slice(0, 5).map((doc) => (
              <div
                key={doc.id}
                onClick={() => onDocumentClick(doc)}
                className="flex items-center gap-3 p-3 rounded-xl hover:bg-muted cursor-pointer group border border-transparent hover:border-primary/20"
              >
                <div className="w-8 h-8 bg-muted rounded-lg flex items-center justify-center group-hover:bg-primary/20">
                  <FileText className="w-4 h-4 text-muted-foreground group-hover:text-primary" />
                </div>
                <div className="flex-1 min-w-0">
                  <div className="text-sm font-medium text-foreground truncate">{doc.title}</div>
                  <div className="text-xs text-muted-foreground">{new Date(doc.updated_at).toLocaleDateString()}</div>
                </div>
                <Badge variant={doc.status === "completed" ? "default" : "secondary"} className="text-xs">
                  {doc.status}
                </Badge>
              </div>
            ))}
          </ScrollArea>
        </div>

        {/* User Profile */}
        <div className="p-6 border-t border-border">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <div className="flex items-center gap-3 p-3 rounded-xl hover:bg-muted cursor-pointer">
                <Avatar className="w-10 h-10 ring-2 ring-primary/20">
                  <AvatarFallback className="bg-gradient-to-r from-primary to-secondary text-primary-foreground font-semibold">
                    {getUserInitials()}
                  </AvatarFallback>
                </Avatar>
                <div className="flex-1">
                  <div className="text-sm font-semibold text-foreground">{getUserDisplayName()}</div>
                  <div className="text-xs text-muted-foreground">{user?.plan_type || "Free"} Plan</div>
                </div>
              </div>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-56 bg-card border-border">
              <DropdownMenuItem
                onClick={() => setShowSettings(true)}
                className="text-foreground hover:text-primary hover:bg-muted"
              >
                <Settings className="w-4 h-4 mr-2" />
                Settings
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={handleLogout}
                className="text-foreground hover:text-destructive hover:bg-muted"
              >
                <LogOut className="w-4 h-4 mr-2" />
                Sign Out
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Mobile Header */}
        {isMobile && (
          <div className="flex items-center justify-between p-4 border-b border-border bg-background">
            <Button
              variant="ghost"
              size="icon"
              onClick={onToggleMobileMenu}
              className="text-muted-foreground hover:text-primary"
            >
              <Menu className="w-6 h-6" />
            </Button>
            <div className="text-lg font-bold text-foreground">Documents</div>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setShowSettings(true)}
              className="text-muted-foreground hover:text-primary"
            >
              <Settings className="w-6 h-6" />
            </Button>
          </div>
        )}

        <div className="flex-1 p-4 lg:p-8 overflow-auto">
          <div className="max-w-7xl mx-auto">
            {/* Header */}
            <div className="mb-6 lg:mb-8">
              <h1 className="text-2xl lg:text-4xl font-bold text-foreground mb-2">Generated Documents</h1>
              <p className="text-sm lg:text-base text-muted-foreground">Manage and access all your created documents</p>
            </div>

            {/* Search and Filter */}
            <div className="mb-6 lg:mb-8 flex flex-col sm:flex-row gap-4">
              <div className="relative flex-1 max-w-md">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4 lg:w-5 lg:h-5" />
                <Input
                  placeholder="Search documents..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-9 lg:pl-10 h-10 lg:h-12 bg-background border-border text-foreground placeholder:text-muted-foreground focus:border-primary focus:ring-primary/20 text-sm lg:text-base"
                />
              </div>
              <Button
                variant="outline"
                className="h-10 lg:h-12 px-4 lg:px-6 border-border text-foreground hover:bg-muted hover:text-primary text-sm lg:text-base"
              >
                <Filter className="w-4 h-4 mr-2" />
                Filter
              </Button>
            </div>

            {/* Documents Grid */}
            <DocumentGrid
              documents={filteredDocuments}
              documentsLoading={documentsLoading}
              onDocumentClick={onDocumentClick}
              documentOperations={documentOperations}
            />
          </div>
        </div>
      </div>
    </div>
  )
}
