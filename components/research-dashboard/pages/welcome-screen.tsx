"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import {
  Plus,
  Clock,
  FolderOpen,
  FileText,
  Settings,
  LogOut,
  Loader2,
  Menu,
  Code,
  Share,
} from "lucide-react"
import { MobileMenu } from "../components/mobile-menu"
import { SettingsDialog } from "@/components/settings-dialog"
import { useAuth } from "@/lib/contexts/auth-context"
import type { Document, AppState, DocumentOperations } from "../types"

interface WelcomeScreenProps {
  documents: Document[]
  documentsLoading: boolean
  documentOperations: DocumentOperations
  onStateChange: (state: AppState) => void
  onDocumentClick: (doc: Document) => void
  onShowSettings: () => void
  isMobile: boolean
  mobileMenuOpen: boolean
  onToggleMobileMenu: () => void
  onCloseMobileMenu: () => void
}

export function WelcomeScreen({
  documents,
  documentsLoading,
  documentOperations,
  onStateChange,
  onDocumentClick,
  onShowSettings,
  isMobile,
  mobileMenuOpen,
  onToggleMobileMenu,
  onCloseMobileMenu,
}: WelcomeScreenProps) {
  const [message, setMessage] = useState("")
  const [isCreating, setIsCreating] = useState(false)
  const [showSettings, setShowSettings] = useState(false)
  const { user, signOut } = useAuth()

  const handleStartPDF = async () => {
    if (message.trim() && !isCreating) {
      try {
        setIsCreating(true)
        const result = await documentOperations.createDocumentWithMessage({
          message: message.trim(),
          type: "research_paper",
        })
        onDocumentClick(result.document)
      } catch (error) {
        console.error("Failed to create document:", error)
      } finally {
        setIsCreating(false)
      }
    }
  }

  const handleLogout = async () => {
    try {
      await signOut()
    } catch (error) {
      console.error("Logout failed:", error)
    }
  }

  const getUserInitials = () => {
    if (!user) return "U"
    return `${user.first_name?.[0] || ""}${user.last_name?.[0] || ""}`.toUpperCase()
  }

  const getUserDisplayName = () => {
    if (!user) return "User"
    return `${user.first_name || ""} ${user.last_name || ""}`.trim() || user.email
  }

  return (
    <div className="flex h-screen bg-background watercolor-wash">
      {/* Settings Dialog */}
      <SettingsDialog open={showSettings} onOpenChange={setShowSettings} />

      {/* Mobile Menu */}
      <MobileMenu
        isOpen={mobileMenuOpen}
        onClose={onCloseMobileMenu}
        currentPage="welcome"
        onNavigate={onStateChange}
        onBackToWelcome={() => {}}
        documents={documents}
        documentsLoading={documentsLoading}
        onDocumentClick={onDocumentClick}
        title="Navigation Menu"
      />

      {/* Professional Sidebar - Desktop */}
      <div
        className={`${
          isMobile ? "hidden" : "w-80"
        } bg-card border-r border-border shadow-xl flex flex-col`}
      >
        {/* Header */}
        <div className="p-6 lg:p-8 border-b border-border">
          <div className="flex items-center gap-3">
            <img src="/logo.png" alt="deepdocx logo" className="w-32 lg:w-40 h-auto" />
          </div>
          <p className="text-sm text-muted-foreground mt-2">Professional Document Suite</p>
        </div>

        {/* Navigation */}
        <div className="p-6 space-y-2">
          <Button className="w-full justify-start bg-primary hover:bg-primary/90 text-primary-foreground h-12 font-medium">
            <Plus className="w-5 h-5 mr-3" />
            New Document
          </Button>

          <Button
            onClick={() => onStateChange("generated-documents")}
            variant="ghost"
            className="w-full justify-start text-foreground hover:text-primary hover:bg-muted h-12 font-medium"
          >
            <Clock className="w-5 h-5 mr-3" />
            Generated Documents
          </Button>

          <Button
            onClick={() => onStateChange("files-management")}
            variant="ghost"
            className="w-full justify-start text-foreground hover:text-primary hover:bg-muted h-12 font-medium"
          >
            <FolderOpen className="w-5 h-5 mr-3" />
            Files Management
          </Button>
        </div>

        {/* Recent Documents */}
        <div className="flex-1 px-6">
          <div className="mb-4">
            <h3 className="text-sm font-semibold text-foreground uppercase tracking-wider">Recent Documents</h3>
          </div>
          <ScrollArea className="space-y-2">
            {documentsLoading ? (
              <div className="flex items-center justify-center py-8">
                <Loader2 className="w-6 h-6 animate-spin text-muted-foreground" />
              </div>
            ) : (
              documents.slice(0, 5).map((doc) => (
                <div
                  key={doc.id}
                  onClick={() => onDocumentClick(doc)}
                  className="flex items-center gap-3 p-3 rounded-xl hover:bg-muted cursor-pointer group border border-transparent hover:border-primary/20"
                >
                  <div className="w-10 h-10 bg-muted rounded-lg flex items-center justify-center group-hover:bg-primary/20">
                    <FileText className="w-5 h-5 text-muted-foreground group-hover:text-primary" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="text-sm font-medium text-foreground truncate">{doc.title}</div>
                    <div className="text-xs text-muted-foreground">{new Date(doc.updated_at).toLocaleDateString()}</div>
                  </div>
                  <Badge variant={doc.status === "completed" ? "default" : "secondary"} className="text-xs">
                    {doc.status}
                  </Badge>
                </div>
              ))
            )}
          </ScrollArea>
        </div>

        {/* User Profile */}
        <div className="p-6 border-t border-border">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <div className="flex items-center gap-3 p-3 rounded-xl hover:bg-muted cursor-pointer">
                <Avatar className="w-12 h-12 ring-2 ring-primary/20">
                  <AvatarFallback className="bg-gradient-to-r from-primary to-secondary text-primary-foreground font-semibold">
                    {getUserInitials()}
                  </AvatarFallback>
                </Avatar>
                <div className="flex-1">
                  <div className="text-sm font-semibold text-foreground">{getUserDisplayName()}</div>
                  <div className="text-xs text-muted-foreground">{user?.plan_type || "Free"} Plan • {documents.length} docs</div>
                </div>
                <Button variant="ghost" size="icon" className="text-muted-foreground hover:text-primary">
                  <Settings className="w-4 h-4" />
                </Button>
              </div>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-56 bg-card border-border">
              <DropdownMenuItem
                onClick={() => setShowSettings(true)}
                className="text-foreground hover:text-primary hover:bg-muted"
              >
                <Settings className="w-4 h-4 mr-2" />
                Settings
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={handleLogout}
                className="text-foreground hover:text-destructive hover:bg-muted"
              >
                <LogOut className="w-4 h-4 mr-2" />
                Sign Out
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Welcome Content */}
      <div className="flex-1 flex flex-col">
        {/* Mobile Header */}
        {isMobile && (
          <div className="flex items-center justify-between p-4 border-b border-border bg-background">
            <Button
              variant="ghost"
              size="icon"
              onClick={onToggleMobileMenu}
              className="text-muted-foreground hover:text-primary"
            >
              <Menu className="w-6 h-6" />
            </Button>
            <img src="/logo.png" alt="deepdocx logo" className="w-24 h-auto" />
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setShowSettings(true)}
              className="text-muted-foreground hover:text-primary"
            >
              <Settings className="w-6 h-6" />
            </Button>
          </div>
        )}

        <div className="flex-1 flex items-center justify-center p-4 lg:p-8">
          <div className="text-center max-w-4xl w-full">
            {/* Hero Section */}
            <div className="mb-8 lg:mb-12">
              <h1 className="text-3xl sm:text-4xl lg:text-6xl font-bold text-foreground mb-4 lg:mb-6 leading-tight handwritten-heading">
                Professional Document
                <span className="bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
                  {" "}
                  Creation
                </span>
              </h1>
              <p className="text-base sm:text-lg lg:text-xl text-muted-foreground leading-relaxed max-w-2xl mx-auto px-4">
                Create, edit, and manage professional documents with AI assistance. From research papers to business
                reports, streamline your workflow with intelligent document generation.
              </p>
            </div>

            {/* Features */}
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-6 lg:gap-8 mb-8 lg:mb-12 px-4">
              <div className="text-center">
                <div className="w-12 h-12 lg:w-16 lg:h-16 bg-primary/20 rounded-2xl flex items-center justify-center mx-auto mb-4 border border-primary/30">
                  <FileText className="w-6 h-6 lg:w-8 lg:h-8 text-primary" />
                </div>
                <h3 className="font-semibold text-foreground mb-2 text-sm lg:text-base">AI-Powered Writing</h3>
                <p className="text-xs lg:text-sm text-muted-foreground">Generate content with advanced AI assistance</p>
              </div>
              <div className="text-center">
                <div className="w-12 h-12 lg:w-16 lg:h-16 bg-secondary/20 rounded-2xl flex items-center justify-center mx-auto mb-4 border border-secondary/30">
                  <Code className="w-6 h-6 lg:w-8 lg:h-8 text-secondary" />
                </div>
                <h3 className="font-semibold text-foreground mb-2 text-sm lg:text-base">LaTeX Support</h3>
                <p className="text-xs lg:text-sm text-muted-foreground">Professional typesetting with LaTeX</p>
              </div>
              <div className="text-center">
                <div className="w-12 h-12 lg:w-16 lg:h-16 bg-chart-2/20 rounded-2xl flex items-center justify-center mx-auto mb-4 border border-chart-2/30">
                  <Share className="w-6 h-6 lg:w-8 lg:h-8 text-chart-2" />
                </div>
                <h3 className="font-semibold text-foreground mb-2 text-sm lg:text-base">Easy Sharing</h3>
                <p className="text-xs lg:text-sm text-muted-foreground">Share and collaborate seamlessly</p>
              </div>
            </div>

            {/* CTA */}
            <div className="max-w-4xl mx-auto px-4">
              <div className="relative">
                <Input
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  placeholder="Describe the document you want to create..."
                  className="h-12 lg:h-16 text-base lg:text-lg pl-4 lg:pl-6 pr-16 lg:pr-20 rounded-2xl border-2 border-primary focus:border-primary focus:ring-4 focus:ring-primary/20 shadow-lg bg-background text-foreground placeholder:text-muted-foreground"
                  onKeyDown={(e) => e.key === "Enter" && handleStartPDF()}
                />
                <Button
                  onClick={handleStartPDF}
                  disabled={!message.trim() || isCreating}
                  className="absolute right-2 top-2 h-8 lg:h-12 px-3 lg:px-6 bg-primary hover:bg-primary/90 text-primary-foreground rounded-xl shadow-lg disabled:opacity-50 font-medium text-sm lg:text-base"
                >
                  {isCreating ? <Loader2 className="w-4 h-4 animate-spin" /> : isMobile ? "Create" : "Create Document"}
                </Button>
              </div>
              <p className="text-xs lg:text-sm text-muted-foreground mt-4 text-center">
                Press Enter or click "Create Document" to start • AI will help you build it step by step
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
