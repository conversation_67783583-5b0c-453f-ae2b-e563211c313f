"use client"

import React, { useState, useRef, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { toast } from "@/components/ui/use-toast"
import {
  ArrowLeft,
  Plus,
  Clock,
  FolderOpen,
  Search,
  Filter,
  Menu,
  Settings,
  LogOut,
  Upload,
  FolderPlus,
  File,
  FileText,
  ImageIcon,
  FileSpreadsheet,
  FolderIcon,
  MoreVertical,
  Download,
  Trash2,
  <PERSON>,
  Chev<PERSON>R<PERSON>,
  <PERSON>ader2,
  <PERSON><PERSON>,
} from "lucide-react"
import { MobileMenu } from "../components/mobile-menu"
import { SettingsDialog } from "@/components/settings-dialog"
import { useAuth } from "@/lib/contexts/auth-context"
import { apiClient } from "@/lib/api/client"
import type { AppState, DatabaseFile } from "@/lib/types"

interface Folder {
  id: string
  name: string
  parent_folder_id?: string
  created_at: string
  updated_at: string
}

interface FilesManagementProps {
  onStateChange: (state: AppState) => void
  onBackToWelcome: () => void
  onShowSettings: () => void
  isMobile: boolean
  mobileMenuOpen: boolean
  onToggleMobileMenu: () => void
  onCloseMobileMenu: () => void
}

export function FilesManagement({
  onStateChange,
  onBackToWelcome,
  onShowSettings,
  isMobile,
  mobileMenuOpen,
  onToggleMobileMenu,
  onCloseMobileMenu,
}: FilesManagementProps) {
  const [searchTerm, setSearchTerm] = useState("")
  const [currentFolder, setCurrentFolder] = useState<string | null>(null)
  const [currentFolderPath, setCurrentFolderPath] = useState<Folder[]>([])
  const [folders, setFolders] = useState<Folder[]>([])
  const [files, setFiles] = useState<DatabaseFile[]>([])
  const [documents, setDocuments] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [newFolderName, setNewFolderName] = useState("")
  const [showCreateFolderDialog, setShowCreateFolderDialog] = useState(false)
  const [isCreatingFolder, setIsCreatingFolder] = useState(false)
  const [uploading, setUploading] = useState(false)
  const [showSettings, setShowSettings] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)
  const { user, signOut } = useAuth()

  // Load folder contents
  useEffect(() => {
    loadFolderContents()
    if (currentFolder) {
      loadFolderPath()
    } else {
      setCurrentFolderPath([])
    }
  }, [currentFolder])

  const loadFolderContents = async () => {
    try {
      setLoading(true)
      const response = await apiClient.folders.getContents(currentFolder || undefined)

      setFolders(response.subfolders || [])
      setFiles(response.files || [])
      setDocuments(response.documents || [])
    } catch (error) {
      console.error("Error loading folder contents:", error)
      toast({
        title: "Error",
        description: "Failed to load folder contents",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const loadFolderPath = async () => {
    if (!currentFolder) return

    try {
      const path = await apiClient.folders.getPath(currentFolder)
      setCurrentFolderPath(path || [])
    } catch (error) {
      console.error("Error loading folder path:", error)
      setCurrentFolderPath([])
    }
  }

  const getFileIcon = (type: string) => {
    switch (type) {
      case "pdf":
        return <FileText className="w-5 h-5 text-red-400" />
      case "docx":
      case "doc":
        return <FileText className="w-5 h-5 text-blue-400" />
      case "png":
      case "jpeg":
      case "jpg":
      case "gif":
        return <ImageIcon className="w-5 h-5 text-green-400" />
      case "csv":
      case "xlsx":
        return <FileSpreadsheet className="w-5 h-5 text-orange-400" />
      case "folder":
        return <FolderIcon className="w-5 h-5 text-yellow-400" />
      default:
        return <File className="w-5 h-5 text-gray-400" />
    }
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 Bytes"
    const k = 1024
    const sizes = ["Bytes", "KB", "MB", "GB"]
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i]
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString()
  }

  const handleCreateFolder = async () => {
    if (!newFolderName.trim()) return

    try {
      setIsCreatingFolder(true)
      
      const newFolder = await apiClient.folders.create({
        name: newFolderName.trim(),
        parent_folder_id: currentFolder || undefined,
      })

      setFolders(prev => [...prev, newFolder])
      setNewFolderName("")
      setShowCreateFolderDialog(false)
      
      toast({
        title: "Success",
        description: "Folder created successfully",
      })
    } catch (error) {
      console.error("Error creating folder:", error)
      toast({
        title: "Error",
        description: "Failed to create folder",
        variant: "destructive",
      })
    } finally {
      setIsCreatingFolder(false)
    }
  }

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const uploadedFiles = event.target.files
    if (!uploadedFiles || uploadedFiles.length === 0) return

    try {
      setUploading(true)
      
      const uploadPromises = Array.from(uploadedFiles).map(file =>
        apiClient.files.upload(file, currentFolder || undefined)
      )

      const newFiles = await Promise.all(uploadPromises)
      setFiles(prev => [...prev, ...newFiles])

      toast({
        title: "Success",
        description: `${uploadedFiles.length} file(s) uploaded successfully`,
      })
      
      // Clear the input
      if (fileInputRef.current) {
        fileInputRef.current.value = ""
      }
    } catch (error) {
      console.error("Error uploading files:", error)
      toast({
        title: "Error",
        description: "Failed to upload files",
        variant: "destructive",
      })
    } finally {
      setUploading(false)
    }
  }

  const handleUploadClick = () => {
    fileInputRef.current?.click()
  }

  const handleDeleteFolder = async (folderId: string) => {
    try {
      await apiClient.folders.delete(folderId)
      setFolders(prev => prev.filter(f => f.id !== folderId))
      toast({
        title: "Success",
        description: "Folder deleted successfully",
      })
    } catch (error) {
      console.error("Error deleting folder:", error)
      toast({
        title: "Error",
        description: "Failed to delete folder",
        variant: "destructive",
      })
    }
  }

  const handleDeleteFile = async (fileId: string) => {
    try {
      await apiClient.files.delete(fileId)
      setFiles(prev => prev.filter(f => f.id !== fileId))
      toast({
        title: "Success",
        description: "File deleted successfully",
      })
    } catch (error) {
      console.error("Error deleting file:", error)
      toast({
        title: "Error",
        description: "Failed to delete file",
        variant: "destructive",
      })
    }
  }

  const handleDownloadFile = async (file: DatabaseFile) => {
    try {
      // Create a download link using the file URL
      const link = document.createElement('a')
      link.href = file.url
      link.download = file.name
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      toast({
        title: "Success",
        description: `${file.name} download started`,
      })
    } catch (error) {
      console.error("Error downloading file:", error)
      toast({
        title: "Error",
        description: "Failed to download file",
        variant: "destructive",
      })
    }
  }

  const handleCopyImageReference = async (file: DatabaseFile) => {
    try {
      // Create folder path for @mention
      let folderPath = ""
      if (currentFolderPath.length > 0) {
        folderPath = currentFolderPath.map(f => f.name).join("/") + "/"
      }

      // Create both @mention and direct path references
      const mentionReference = `@${folderPath}${file.name}`
      const directReference = folderPath ? `${folderPath}${file.name}` : file.name

      // Use the mention reference as primary
      const reference = mentionReference
      await navigator.clipboard.writeText(reference)

      toast({
        title: "Success",
        description: `Image reference copied: ${reference}`,
      })
    } catch (error) {
      console.error("Error copying image reference:", error)
      toast({
        title: "Error",
        description: "Failed to copy image reference",
        variant: "destructive",
      })
    }
  }

  const isImageFile = (fileType: string) => {
    return ['png', 'jpg', 'jpeg', 'gif', 'svg', 'webp'].includes(fileType.toLowerCase())
  }

  const filteredFiles = files.filter(file =>
    file.name.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const filteredFolders = folders.filter(folder =>
    folder.name.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const filteredDocuments = documents.filter(doc =>
    doc.title.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const handleLogout = async () => {
    try {
      await signOut()
    } catch (error) {
      console.error("Logout failed:", error)
    }
  }

  const getUserInitials = () => {
    if (!user) return "U"
    return `${user.first_name?.[0] || ""}${user.last_name?.[0] || ""}`.toUpperCase()
  }

  const getUserDisplayName = () => {
    if (!user) return "User"
    return `${user.first_name || ""} ${user.last_name || ""}`.trim() || user.email
  }

  if (loading) {
    return (
      <div className="flex h-screen bg-background watercolor-wash items-center justify-center">
        <Loader2 className="w-8 h-8 animate-spin text-primary" />
      </div>
    )
  }

  return (
    <div className="flex h-screen bg-background watercolor-wash">
      {/* Settings Dialog */}
      <SettingsDialog open={showSettings} onOpenChange={setShowSettings} />
      {/* Mobile Menu */}
      <MobileMenu
        isOpen={mobileMenuOpen}
        onClose={onCloseMobileMenu}
        currentPage="files-management"
        onNavigate={onStateChange}
        onBackToWelcome={onBackToWelcome}
        documents={documents}
        onDocumentClick={() => {}}
      />

      {/* Sidebar - Desktop */}
      <div className={`${isMobile ? "hidden" : "w-80"} bg-card border-r border-border shadow-lg flex flex-col`}>
        <div className="p-6 lg:p-8 border-b border-border">
          <div className="flex items-center gap-3">
            <Button
              variant="ghost"
              size="icon"
              onClick={onBackToWelcome}
              className="text-muted-foreground hover:text-primary"
            >
              <ArrowLeft className="w-5 h-5" />
            </Button>
            <img src="/logo.png" alt="deepdocx logo" className="w-32 h-auto" />
          </div>
        </div>

        <div className="p-6 space-y-2">
          <Button
            onClick={() => onStateChange("welcome")}
            variant="ghost"
            className="w-full justify-start text-foreground hover:text-primary hover:bg-muted h-12"
          >
            <Plus className="w-5 h-5 mr-3" />
            New Document
          </Button>

          <Button
            onClick={() => onStateChange("generated-documents")}
            variant="ghost"
            className="w-full justify-start text-foreground hover:text-primary hover:bg-muted h-12"
          >
            <Clock className="w-5 h-5 mr-3" />
            Generated Documents
          </Button>

          <Button className="w-full justify-start bg-primary hover:bg-primary/90 text-primary-foreground h-12">
            <FolderOpen className="w-5 h-5 mr-3" />
            Files Management
          </Button>
        </div>

        {/* User Profile */}
        <div className="flex-1"></div>
        <div className="p-6 border-t border-border">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <div className="flex items-center gap-3 p-3 rounded-xl hover:bg-muted cursor-pointer">
                <Avatar className="w-10 h-10 ring-2 ring-primary/20">
                  <AvatarFallback className="bg-gradient-to-r from-primary to-secondary text-primary-foreground font-semibold">
                    {getUserInitials()}
                  </AvatarFallback>
                </Avatar>
                <div className="flex-1">
                  <div className="text-sm font-semibold text-foreground">{getUserDisplayName()}</div>
                  <div className="text-xs text-muted-foreground">{user?.plan_type || "Free"} Plan</div>
                </div>
              </div>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-56 bg-card border-border">
              <DropdownMenuItem
                onClick={() => setShowSettings(true)}
                className="text-foreground hover:text-primary hover:bg-muted"
              >
                <Settings className="w-4 h-4 mr-2" />
                Settings
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={handleLogout}
                className="text-foreground hover:text-destructive hover:bg-muted"
              >
                <LogOut className="w-4 h-4 mr-2" />
                Sign Out
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Mobile Header */}
        {isMobile && (
          <div className="flex items-center justify-between p-4 border-b border-border bg-background">
            <Button
              variant="ghost"
              size="icon"
              onClick={onToggleMobileMenu}
              className="text-muted-foreground hover:text-primary"
            >
              <Menu className="w-6 h-6" />
            </Button>
            <div className="text-lg font-bold text-foreground">Files</div>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setShowSettings(true)}
              className="text-muted-foreground hover:text-primary"
            >
              <Settings className="w-6 h-6" />
            </Button>
          </div>
        )}

        <div className="flex-1 p-4 lg:p-8 overflow-auto">
          <div className="max-w-7xl mx-auto">
            {/* Header */}
            <div className="mb-6 lg:mb-8">
              <div className="flex items-center gap-2 mb-2">
                {currentFolder && currentFolderPath.length > 0 ? (
                  <>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setCurrentFolder(null)}
                      className="text-muted-foreground hover:text-primary text-sm lg:text-base"
                    >
                      Files
                    </Button>
                    {currentFolderPath.map((folder, index) => (
                      <React.Fragment key={folder.id}>
                        <ChevronRight className="w-4 h-4 text-muted-foreground" />
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setCurrentFolder(folder.id)}
                          className={`text-sm lg:text-base ${
                            index === currentFolderPath.length - 1
                              ? "font-semibold text-foreground"
                              : "text-muted-foreground hover:text-primary"
                          }`}
                        >
                          {folder.name}
                        </Button>
                      </React.Fragment>
                    ))}
                  </>
                ) : currentFolder ? (
                  <>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setCurrentFolder(null)}
                      className="text-muted-foreground hover:text-primary text-sm lg:text-base"
                    >
                      Files
                    </Button>
                    <ChevronRight className="w-4 h-4 text-muted-foreground" />
                    <span className="font-semibold text-foreground text-sm lg:text-base">
                      Current Folder
                    </span>
                  </>
                ) : (
                  <h1 className="text-2xl lg:text-4xl font-bold text-foreground">Files Management</h1>
                )}
              </div>
              <p className="text-sm lg:text-base text-muted-foreground">Organize and manage your files and folders</p>
            </div>

            {/* Breadcrumb Navigation */}
            {currentFolder && (
              <div className="mb-4 flex items-center gap-2 text-sm text-muted-foreground">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setCurrentFolder(null)}
                  className="text-muted-foreground hover:text-primary p-1 h-auto"
                >
                  <ArrowLeft className="w-4 h-4 mr-1" />
                  Back to All Folders
                </Button>
              </div>
            )}

            {/* Actions */}
            <div className="mb-6 lg:mb-8 flex flex-col sm:flex-row gap-4">
              <Dialog open={showCreateFolderDialog} onOpenChange={setShowCreateFolderDialog}>
                <DialogTrigger asChild>
                  <Button className="bg-primary hover:bg-primary/90 text-primary-foreground shadow-lg h-10 lg:h-12 text-sm lg:text-base">
                    <FolderPlus className="w-4 h-4 mr-2" />
                    New Folder
                  </Button>
                </DialogTrigger>
                <DialogContent className="sm:max-w-md bg-card border-border">
                  <DialogHeader>
                    <DialogTitle className="text-foreground">Create New Folder</DialogTitle>
                    <DialogDescription className="text-muted-foreground">
                      Enter a name for your new folder{currentFolder ? " in the current folder" : ""}
                    </DialogDescription>
                  </DialogHeader>
                  <div className="space-y-4">
                    <Input
                      value={newFolderName}
                      onChange={(e) => setNewFolderName(e.target.value)}
                      placeholder="Folder name"
                      className="bg-background border-border text-foreground"
                      onKeyDown={(e) => e.key === "Enter" && handleCreateFolder()}
                    />
                    <div className="flex gap-2">
                      <Button
                        onClick={handleCreateFolder}
                        disabled={!newFolderName.trim() || isCreatingFolder}
                        className="flex-1 bg-primary hover:bg-primary/90 text-primary-foreground"
                      >
                        {isCreatingFolder ? <Loader2 className="w-4 h-4 mr-2 animate-spin" /> : null}
                        Create
                      </Button>
                      <Button
                        variant="outline"
                        onClick={() => setShowCreateFolderDialog(false)}
                        className="border-border text-foreground hover:bg-muted"
                      >
                        Cancel
                      </Button>
                    </div>
                  </div>
                </DialogContent>
              </Dialog>

              <Button
                onClick={handleUploadClick}
                disabled={uploading}
                variant="outline"
                className="border-border text-foreground hover:bg-muted hover:text-primary h-10 lg:h-12 text-sm lg:text-base"
              >
                {uploading ? <Loader2 className="w-4 h-4 mr-2 animate-spin" /> : <Upload className="w-4 h-4 mr-2" />}
                Upload Files
              </Button>

              <input
                ref={fileInputRef}
                type="file"
                multiple
                onChange={handleFileUpload}
                className="hidden"
              />
            </div>

            {/* Search and Filter */}
            <div className="mb-6 lg:mb-8 flex flex-col sm:flex-row gap-4">
              <div className="relative flex-1 max-w-md">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4 lg:w-5 lg:h-5" />
                <Input
                  placeholder="Search files and folders..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-9 lg:pl-10 h-10 lg:h-12 bg-background border-border text-foreground placeholder:text-muted-foreground focus:border-primary focus:ring-primary/20 text-sm lg:text-base"
                />
              </div>
              <Button
                variant="outline"
                className="h-10 lg:h-12 px-4 lg:px-6 border-border text-foreground hover:bg-muted hover:text-primary text-sm lg:text-base"
              >
                <Filter className="w-4 h-4 mr-2" />
                Filter
              </Button>
            </div>

            {/* Files and Folders Grid */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 lg:gap-6">
              {/* Folders */}
              {filteredFolders.map((folder) => (
                <Card
                  key={folder.id}
                  className="hover:shadow-xl cursor-pointer group border-border bg-card hover:border-primary/50 hover:shadow-primary/20"
                  onClick={() => setCurrentFolder(folder.id)}
                >
                  <CardHeader className="pb-4">
                    <div className="flex items-start justify-between">
                      <div className="flex items-center gap-3">
                        <div className="w-12 h-12 bg-gradient-to-r from-chart-1 to-chart-2 rounded-xl flex items-center justify-center shadow-lg">
                          <FolderIcon className="w-6 h-6 text-primary-foreground" />
                        </div>
                      </div>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="opacity-0 group-hover:opacity-100 text-muted-foreground hover:text-primary"
                            onClick={(e) => e.stopPropagation()}
                          >
                            <MoreVertical className="w-4 h-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent className="bg-card border-border">
                          <DropdownMenuItem className="text-foreground hover:text-primary hover:bg-muted">
                            <Edit className="w-4 h-4 mr-2" />
                            Rename
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            className="text-destructive hover:text-destructive/80 hover:bg-muted"
                            onClick={(e) => {
                              e.stopPropagation()
                              handleDeleteFolder(folder.id)
                            }}
                          >
                            <Trash2 className="w-4 h-4 mr-2" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                    <CardTitle className="text-lg group-hover:text-primary leading-tight text-foreground">
                      {folder.name}
                    </CardTitle>
                    <CardDescription className="text-sm text-muted-foreground">
                      Created {formatDate(folder.created_at)}
                    </CardDescription>
                  </CardHeader>
                </Card>
              ))}

              {/* Documents */}
              {filteredDocuments.map((document) => (
                <Card
                  key={document.id}
                  className="hover:shadow-xl cursor-pointer group border-border bg-card hover:border-primary/50 hover:shadow-primary/20"
                  onClick={() => onStateChange("document-editor")}
                >
                  <CardHeader className="pb-4">
                    <div className="flex items-start justify-between">
                      <div className="flex items-center gap-3">
                        <div className="w-12 h-12 bg-gradient-to-r from-primary to-secondary rounded-xl flex items-center justify-center shadow-lg">
                          <FileText className="w-6 h-6 text-primary-foreground" />
                        </div>
                      </div>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="opacity-0 group-hover:opacity-100 text-muted-foreground hover:text-primary"
                            onClick={(e) => e.stopPropagation()}
                          >
                            <MoreVertical className="w-4 h-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent className="bg-card border-border">
                          <DropdownMenuItem className="text-foreground hover:text-primary hover:bg-muted">
                            <Edit className="w-4 h-4 mr-2" />
                            Edit
                          </DropdownMenuItem>
                          <DropdownMenuItem className="text-destructive hover:text-destructive/80 hover:bg-muted">
                            <Trash2 className="w-4 h-4 mr-2" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                    <CardTitle className="text-lg group-hover:text-primary leading-tight text-foreground">
                      {document.title}
                    </CardTitle>
                    <CardDescription className="text-sm text-muted-foreground">
                      {document.type} • {document.status}
                    </CardDescription>
                  </CardHeader>
                </Card>
              ))}

              {/* Files */}
              {filteredFiles.map((file) => (
                <Card
                  key={file.id}
                  className="hover:shadow-xl cursor-pointer group border-border bg-card hover:border-primary/50 hover:shadow-primary/20"
                >
                  <CardHeader className="pb-4">
                    <div className="flex items-start justify-between">
                      <div className="flex items-center gap-3">
                        <div className="relative">
                          <div className={`w-12 h-12 rounded-xl flex items-center justify-center shadow-lg ${
                            isImageFile(file.type)
                              ? "bg-gradient-to-r from-green-400 to-blue-500"
                              : "bg-muted"
                          }`}>
                            {getFileIcon(file.type)}
                          </div>
                          {isImageFile(file.type) && (
                            <div className="absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
                              <ImageIcon className="w-2 h-2 text-white" />
                            </div>
                          )}
                        </div>
                      </div>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="opacity-0 group-hover:opacity-100 text-muted-foreground hover:text-primary"
                            onClick={(e) => e.stopPropagation()}
                          >
                            <MoreVertical className="w-4 h-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent className="bg-card border-border">
                          <DropdownMenuItem
                            className="text-foreground hover:text-primary hover:bg-muted"
                            onClick={(e) => {
                              e.stopPropagation()
                              handleDownloadFile(file)
                            }}
                          >
                            <Download className="w-4 h-4 mr-2" />
                            Download
                          </DropdownMenuItem>
                          {isImageFile(file.type) && (
                            <DropdownMenuItem
                              className="text-foreground hover:text-primary hover:bg-muted"
                              onClick={(e) => {
                                e.stopPropagation()
                                handleCopyImageReference(file)
                              }}
                            >
                              <Copy className="w-4 h-4 mr-2" />
                              Copy LaTeX Reference
                            </DropdownMenuItem>
                          )}
                          <DropdownMenuItem className="text-foreground hover:text-primary hover:bg-muted">
                            <Edit className="w-4 h-4 mr-2" />
                            Rename
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            className="text-destructive hover:text-destructive/80 hover:bg-muted"
                            onClick={(e) => {
                              e.stopPropagation()
                              handleDeleteFile(file.id)
                            }}
                          >
                            <Trash2 className="w-4 h-4 mr-2" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                    <CardTitle className="text-lg group-hover:text-primary leading-tight text-foreground">
                      {file.name}
                    </CardTitle>
                    <CardDescription className="text-sm text-muted-foreground">
                      {formatFileSize(file.size || 0)} • {formatDate(file.created_at)}
                    </CardDescription>
                  </CardHeader>
                </Card>
              ))}
            </div>

            {/* Empty State */}
            {filteredFolders.length === 0 && filteredFiles.length === 0 && filteredDocuments.length === 0 && (
              <div className="text-center py-12">
                <FolderIcon className="w-16 h-16 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-foreground mb-2">No items found</h3>
                <p className="text-muted-foreground mb-6">
                  {searchTerm ? "No files or folders match your search." : "This folder is empty. Upload files or create folders to get started."}
                </p>
                {!searchTerm && (
                  <div className="flex gap-4 justify-center">
                    <Button
                      onClick={() => setShowCreateFolderDialog(true)}
                      className="bg-primary hover:bg-primary/90 text-primary-foreground"
                    >
                      <FolderPlus className="w-4 h-4 mr-2" />
                      Create Folder
                    </Button>
                    <Button
                      onClick={handleUploadClick}
                      variant="outline"
                      className="border-border text-foreground hover:bg-muted hover:text-primary"
                    >
                      <Upload className="w-4 h-4 mr-2" />
                      Upload Files
                    </Button>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
